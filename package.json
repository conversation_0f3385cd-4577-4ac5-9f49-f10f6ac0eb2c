{"name": "sds-tag", "version": "3.1.0", "private": true, "description": "Projeto que gera a Tag de Serviço para mostrar as campanhas", "main": "app.ts", "types": "app.d.ts", "author": "SimpleAds <<EMAIL>>", "directories": {"test": "test"}, "scripts": {"test": "echo \"Error: oops, the actor has no tests yet, sad!\" && exit 1", "start": "npm run build:ts && fastify start -l info dist/app.js", "build:ts": "npm run copy:view && tsc", "watch:ts": "tsc -w", "dev": "npm run build:ts && concurrently -k -p \"[{name}]\" -n \"TypeScript,App\" -c \"yellow.bold,cyan.bold\" \"npm:watch:ts\" \"npm:dev:start\"", "dev:start": "fastify start --ignore-watch=.ts$ -w -l info -P dist/app.js --options", "config": "(cp .env.example .env || copy .env.example .env)", "copy:view": "copyfiles -u 1 src/views/*.ejs dist/"}, "repository": {"type": "git", "url": "git+https://github.com/SimpleAds/sds-tag.git"}, "dependencies": {"@fastify/autoload": "^5.7.1", "@fastify/sensible": "^6.0.1", "@fastify/view": "^10.0.1", "axios": "^1.6.8", "copyfiles": "^2.4.1", "ejs": "^3.1.10", "fastify": "^5.1.0", "fastify-cli": "5.8.0", "fastify-plugin": "^4.5.1", "fastify-type-provider-zod": "^4.0.2", "terser": "^5.31.1", "tiny-lru": "^11.0.1"}, "devDependencies": {"@types/ejs": "^3.1.2", "@types/google.analytics": "^0.0.42", "@types/googletag": "^3.0.3", "@types/node": "^20.5.1", "@types/node-fetch": "^2.6.5", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "concurrently": "^8.2.0", "eslint": "^8.47.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "fastify-tsconfig": "^1.0.1", "prettier": "^3.0.2", "ts-node": "^10.9.1", "typescript": "^5.1.6"}}