![SimpleAds](https://static.simpleads.com.br/logo_SimpleAds.gif)

# Tag SimpleAds
API para a Tag da SimpleAds, usada para carregar os anúncios nos sites que a tem implementada.

**Obs:** É necessário ter os paramêtros **smart_site_id**, **smart_page_id** e opcionalmente **smart_format_id**, para gerar as tags corretamente.

## TODO:

- [ ] InArticle: Testar o bug anterior de height e width 0 no prebid com o google: [commit](https://github.com/SimpleAds/formato-inarticle/commit/210b87bfedd547f3017f4bffe5762c96a570e652)
- [ ] Excroll: Alterar forma como o .preExcroll() é definido. Simplificar como o InArticle
- [ ] Excroll: melhorar responsividade

## Observações sobre o Typescript da tag (t. e tag.)
Ao criar novas funções para passar para dentro da tag via template engine `.ejs` é necessário modificar os seguintes arquivos:

- `tag.ts` para exportar as funções
- `tagController.ts` para passar as funções ao template engine
- `document.d.ts` para passar a tipagem ao template engine

Por enquanto esta é a unica maneira de se conseguir passar informações ao bundle dinâmico gerado para cada site.

## Endpoints

| Endpoint (/api)                   | Funcionalidade                              |
|-----------------------------------|---------------------------------------------|
| /cache/del:key | Deletar cache de uma chave específica (sid + pid)
| /cache/clear   | Limpar todo LRU Cache
| /cache/info    | Informações do LRU Cache
| /**v3**/:smart_site_id/:smart_page_id/show.js   | Versão mais atual da tag, onde é englobado todos os scripts necessários para a chamada de anúncios.
| /**v3**/:smart_site_id/:smart_page_id/show.js/:smart_format_id | Versão da tag para funcionar apenas com um formato, onde engloga todos os scripts para a chamada de anúncios. (depreciado)

## Instalação

Instale as dependências necessárias para poder começar corretamente, para isso:

```
npm install
```

Para montar a configuração do projeto será necessário rodar o seguinte comando:

**Obs:** Esse comando vai criar um arquivo **.env** que terá as variáveis de ambientes necessárias para o projeto ser iniciado.

```
npm run config
```

## Iniciando
Após instalar as dependências do projeto, e seguir os passos anteriores você já pode iniciar o projeto: 

- Para modo dev: `npm run dev`
- Para modo produção: `npm run start`


## Instalação da Tag

A implementação deverá ser realizada no elemento `<head>` do site

#### Opção 1: Script Tag - Geral

- O script abaixo irá carregar de uma vez todos os formatos ativos e deverá ser carregado em todas as áreas do site:

    ```html
    <script data-cfasync="false" type="text/javascript">
    	(function (w, d) {
    	var s = d.createElement("script"), h = d.head || d.getElementsByTagName("head")[0];
    	s.src = "https://tag.simpleads.com.br/v3/s147882/p773830/show.js";
    	s.type = "text/javascript";
    	s.async = true;
    	s.setAttribute("data-cfasync", "false");
    	h.appendChild(s);
    	})(window, document);
    </script>
    ```

#### Opção 2: Script Tag - Por formato

- O script abaixo terá o modo **standalone** ativo e deverá ser carregado em todas as áreas do site:

    ```html
    <script type="text/javascript">
    	(function (w, d) {
    		var s = d.createElement("script"), h = d.head || d.getElementsByTagName("head")[0];
    		s.src = "https://tag.simpleads.com.br/v3/s147882/p773830/show.js";
    		s.type = "text/javascript";
    		s.async = true;
    		s.setAttribute("data-standalone", "true");
    		s.setAttribute("data-cfasync", "false");
    		h.appendChild(s);
    	})(window, document);
    </script>
    ```

- Adicionar o comando abaixo para cada formato nas áreas do site mediante a preferência do publisher:

    ```html
    /* EXEMPLO: FORMATO SLIDER */
    <script type="text/javascript">
    	var _sds = window._sds || {};
    	_sds.cmd = _sds.cmd || [];
    	_sds.cmd.push(function() { _sds.loadFormat(52892) });
    </script>
    ```

#### Opção 3: Script Tag - Tamanhos IAB

-  O script abaixo irá carregar apenas um tamanho de anúncio especifico e deve ser implementado dentro dos blocos de anúncios do site

    ```html
    /* EXEMPLO: Arroba - (300x250) */
    <script type="text/javascript">
    	(function (w, d) {
    		var s = d.createElement("script"), h = d.head || d.getElementsByTagName("head")[0];
    		s.src = "https://tag.simpleads.com.br/v3/s147882/p773830/show.js?size=arroba";
    		s.type = "text/javascript";
    		s.async = true;
    		s.setAttribute("data-cfasync", "false");
    		h.appendChild(s);
    	})(window, document);
    </script>
    ```
