{"extends": "fastify-tsconfig", "compilerOptions": {"target": "es2018", "outDir": "dist", "sourceMap": true, "declaration": true, "useUnknownInCatchVariables": false, "noUnusedLocals": false, "noUnusedParameters": false, "esModuleInterop": true, "lib": ["esnext", "DOM"]}, "include": ["src/**/*.ts", "src/**/*.ejs", "src/@types/prebid.d.ts"], "exclude": ["*.ejs", "tsconfig.json", "**/tests/**", "**/node_modules/**", "src/**/*.ejs", "src/**/*.spec.ts", "src/**/*.test.ts", "viewability.ts"]}