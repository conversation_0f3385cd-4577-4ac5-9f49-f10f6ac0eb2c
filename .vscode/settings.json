{
	"editor.rulers": [
    100,
		120,
		140
	],
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "[json]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "[javascript]": {
    // "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    // "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "thunder-client.saveRequestOnSend": false
}