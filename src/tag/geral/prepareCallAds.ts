/* -------------------------------------------------------------------------
Configura e carrega os formatos de anúncios
------------------------------------------------------------------------- */

export async function prepareCallAds(formatId: TFormatIds | undefined = undefined) {
  const formats = !formatId ? pub.formats : [{ id: formatId, tagId: `smp_${formatId}` }]
  const isMobile = _sds.device === 'mobile'
  const sizeCutNote = 450

  formats.map(format => {
    const adSlot = pub.adSlots[format.tagId]

    const filterSizes = adSlot.sizes.filter(ls => (isMobile ? ls[0] < sizeCutNote : ls[0] > sizeCutNote))
    adSlot.sizes = filterSizes.length ? filterSizes : adSlot.sizes

    const setup: IFormatSetup = { formatId: format.id, tagId: format.tagId, containerId: adSlot.code, print_when: 2 }

    console.log('_sds.config', _sds.config)

    if (
      adSlot.code &&
      [52027, 52892, 52025].includes(format.id) &&
      !(isMobile && format.id === 52025) &&
      _sds.config.floater.show
    ) {
      /* -------------------------------------------------------------------------
      | Floater
      ------------------------------------------------------------------------- */
      return (adSlot.instance = new t.Floater(setup))
    }

    if (adSlot.code && format.id === 52894 && _sds.config.excroll.show) {
      /* -------------------------------------------------------------------------
      | Excroll
      ------------------------------------------------------------------------- */
      w.preExcroll()
      return (adSlot.instance = new t.Excroll(setup))
    }

    if (adSlot.code && format.id === 52896 && _sds.config.inarticle.show) {
      /* -------------------------------------------------------------------------
      | InArticle
      ------------------------------------------------------------------------- */
      return (adSlot.instance = new t.InArticle({ ...setup, print_when: 1 }))
    }

    if (adSlot.code && format.id === 52026 && _sds.config.fullscreen.show) {
      /* -------------------------------------------------------------------------
      | Fullscreen
      ------------------------------------------------------------------------- */
      return (adSlot.instance = new t.Fullscreen(setup))
    }
  })
}
