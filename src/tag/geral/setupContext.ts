/* -------------------------------------------------------------------------
| Setar contexto da página, target e setup de usuário
| Aguarda no máximo 5s pela resposta
------------------------------------------------------------------------- */

export async function setupContext() {
  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 5000)
    const b64uri = window.btoa(location.href)
    const ctxparams = `${b64uri}&t=${pub.id}`
    const ctxuri = `https://ctx2.simpleads.com.br/context?url=${ctxparams}`
    const context = await fetch(ctxuri, { method: 'GET', signal: controller.signal })
    clearTimeout(timeoutId)
    const policies = context ? await context.json() : _sds.context

    _sds.context = policies.ct[0] !== 99999 ? policies : _sds.context

    // bloquear governo observatorio digital (solução temporária)
    if (b64uri === 'aHR0cHM6Ly93d3cub2JzZXJ2YWRvcnJlZ2lvbmFsLmNvbS5ici8=') {
      _sds.context.po[3] = 0
    }
  } catch (e) {
    //
  }

  _sds.user = _sds.setupUserData()
  _sds.target = `${_sds.naveggSmartString()}`
  _sds.target += `active_rtb=${pub.active_rtb};`
  _sds.target += `easy_nlp=${_sds.context.ct || []};`
  _sds.target += `easy_bs=${_sds.context.bs || []};`
  _sds.target += _sds.context.po[1] ? 'has_badkeys=false;' : 'has_badkeys=true;'
  _sds.target += _sds.debug.campaign ? `sds_campaign=${_sds.debug.campaign};` : ''
  _sds.target += _sds.user.cookies ? `${_sds.user.cookies};` : ''
  _sds.target += window.location.pathname === '/' ? `is_home=true;` : ''

  // possivelmente depreciado
  _sds.target += _sds.context.po[0] ? 'enable_campaign=true;' : ''
  _sds.target += _sds.context.po[3] ? 'enable_gov=true;' : ''

  _sds.log('[Context]:', _sds.context)
  _sds.log('[Target]:', _sds.target)
}
