// @ts-nocheck

/* -- --------------------------------------------------------------------------
Funcionalidade de Viewability (de preferencia usar a versão pelo pacote )
@see https://github.com/SimpleAds/sds-viewability/
@TODO: verificar colisões/sobreposição, verificar se tamanho do container é válido
-------------------------------------------------------------------------- -- */

export function viewability() {
  return {
    config: null,
    isExcroll: false,
    element: null,
    watchAnimationEl: null,
    watchTransition: null,
    observer: null,
    viewableRatio: 50,
    viewableWait: 999,
    currentViewRatio: 0,
    vaSended: false,
    vaTimer: null,
    listeners: null,
    transitionListener: null,
    hasIntersectionObserver: typeof window !== 'undefined' && 'IntersectionObserver' in window && 'IntersectionObserverEntry' in window && 'intersectionRatio' in window.IntersectionObserverEntry.prototype,

    /* -------------------------------------------------------------------------
    | Iniciar o observador de VA
    | @param config - Objeto com configurações para iniciar observador (vem do svelte)
    | @param watchAnimationEl - Elemento para escutar animação quando tiver
    | @param watchTransition - Propriedade para escutar que será animada
    | @param wait - Tempo para disparar o pixel
    | @param ratio - Porcentagem de visualização do AD para dar o trigger de VA
    ------------------------------------------------------------------------- */
    init (config, watchAnimationEl, watchTransition = 'transform', wait = 1000, ratio = 50) {
      if (this.hasIntersectionObserver) {
        this.config = config
        this.isExcroll = config.formatId === 52894
        this.watchAnimationEl = watchAnimationEl || config.slot
        this.watchTransition = watchTransition
        this.element = config.creativeFrame || config.slot
        this.viewableWait = wait
        this.viewableRatio = ratio

        /*
          devido a problemas com sites pesados e formato com animação de abertura,
          executar o trigger do script de viewability somente quando a transição estiver completa
        */
        this.transitionListener = (e) => {
          e.propertyName === watchTransition && e.target === this.watchAnimationEl && this.createObserver()
        }

        if (config.open_animation) {
          this.watchAnimationEl.addEventListener('transitionend', this.transitionListener)
        } else {
          this.createObserver()
        }

        this.listeners = this.observerMonitor.bind(this)
        d.addEventListener('visibilitychange', this.listeners)

        /* evento para o excroll */
        this.isExcroll && d.addEventListener('scroll', this.listeners)
      } else {
        _sds.warn('[VA]: Browser sem suporte ao hasIntersectionObserver')
      }
    },

    /* -------------------------------------------------------------------------
    | Start do Observador
    ------------------------------------------------------------------------- */
    createObserver () {
      if (!this.observer) {
        this.transitionListener && this.watchAnimationEl.removeEventListener('transitionend', this.transitionListener)

        const options = { rootMargin: '0px', threshold: [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1] }

        this.observer = new IntersectionObserver(entries => {
          const entry = entries[0]

          /* atualizar currentViewRatio e disparar timer */
          this.currentViewRatio = Math.floor(entry.intersectionRatio * 100)
          if (entries.length > 0 && entry.isIntersecting) {
            this.vaTimer = setTimeout(() => this.callVA(), this.viewableWait)
          }

          /* limpar timer caso saia da zona de visualização do banner */
          if (!this.isViewable()) {
            clearTimeout(this.vaTimer)
          }
        }, options)

        this.observer.observe(this.element)
      }
    },

    /* -------------------------------------------------------------------------
    | Destruir observador
    ------------------------------------------------------------------------- */
    destroyObserver () {
      if (this.observer) {
        clearTimeout(this.vaTimer)
        this.observer.unobserve(this.element)
        this.observer.disconnect()
        this.observer = null
      }
    },

    /* -------------------------------------------------------------------------
    | Destruir Listeners (ex: alternancia de abas)
    ------------------------------------------------------------------------- */
    destroyListeners () {
      this.isExcroll && d.removeEventListener('scroll', this.listeners)
      d.removeEventListener('visibilitychange', this.listeners)
    },

    /* -------------------------------------------------------------------------
    | Ativar/Desativar observador caso o documento esteja visível em tela (aba ativa)
    ------------------------------------------------------------------------- */
    observerMonitor () {
      const styles = this.isExcroll ? getComputedStyle(this.config.banner) : {}
      if (d.hidden || styles.opacity < 0.75) {
        this.destroyObserver()
      } else {
        this.createObserver()
      }
    },

    /* -------------------------------------------------------------------------
    | Verifica se o container está visível em tela
    ------------------------------------------------------------------------- */
    isViewable () {
      if (this.validateStyles() && this.hasIntersectionObserver) {
        return this.currentViewRatio >= this.viewableRatio
      }
    },

    /* -------------------------------------------------------------------------
    | Verifica estilos não permitidos nos elementos
    ------------------------------------------------------------------------- */
    validateStyles () {
      let el = this.element

      for (let i = 1; el && el !== document && i <= 4; el = el.parentNode, i++) {
        const { opacity, visibility, display, zIndex, pointerEvents } = window.getComputedStyle(el)
        if (opacity <= 0 || visibility === 'hidden' || display === 'none' || zIndex < 0 || pointerEvents === 'none') {
          return false
        }
      }

      return true
    },

    /* -------------------------------------------------------------------------
    | Criar pixels
    ------------------------------------------------------------------------- */
    callVA () {
      if (this.isViewable() && !this.vaSended && !d.hidden) {
        this.config.onVA && this.config.onVA()
        this.vaSended = true
        this.destroyObserver()
        this.destroyListeners()
      }
    }
  }
}
