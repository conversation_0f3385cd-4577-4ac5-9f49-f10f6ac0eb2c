/* -------------------------------------------------------------------------
| Eventos do Blink
------------------------------------------------------------------------- */

export function blink() {
  const _blink = {
    addBaseCSS() {
      return new Promise((resolve, reject) => {
        if (!d.getElementById('sds-blink-css')) {
          const css = d.createElement('link')
          css.rel = 'stylesheet'
          css.id = 'sds-blink-css'
          css.href = 'https://static.simpleads.com.br/blink/base.css'
          css.onload = () => resolve('CSS Loaded')
          d.head.appendChild(css)
        } else {
          resolve('CSS already added')
        }
      })
    },

    sendPostMessageToBlinkPage: function (data: IDataBlink, target: HTMLIFrameElement) {
      target.contentWindow!.postMessage(
        JSON.stringify({
          ...data,
          isBlinkPage: true
        }),
        '*'
      )
    },

    create(data: IDataBlink) {
      const urlWithQuery = data.url + (data.url.includes('?') ? '&' : '?')
      const srcUrl = `${urlWithQuery}openPage=1`

      const wrapper = d.createElement('div')
      wrapper.id = 'sds-blink-wrapper'

      const iframe = d.createElement('iframe')
      iframe.id = 'sds-blink-iframe'
      iframe.src = srcUrl

      iframe.onload = () => {
        _blink.sendPostMessageToBlinkPage(data, iframe)
      }

      wrapper.appendChild(iframe)
      d.body.appendChild(wrapper)

      return { wrapper, iframe }
    },

    open(data: IDataBlink) {
      d.body.classList.add('sds-noscroll')
      const el = _blink.create(data)

      if (el.wrapper) {
        setTimeout(() => {
          el.wrapper.classList.add('sds-blink-opened')
        }, 50)
      }
    },

    destroy() {
      d.body.classList.remove('sds-noscroll')
      const el = d.getElementById('sds-blink-wrapper')

      if (el) {
        el.classList.remove('sds-blink-opened')
        setTimeout(() => d.body.removeChild(el), 300)
      }
    },

    message(event: MessageEvent) {
      if (!event.data || (event.data.type !== 'blink' && event.data.type !== 'dplus')) return

      _sds.log('[Blink]:', event.data)

      const { data }: { data: IDataBlink } = event
      const authorizedDomains = [
        'http://localhost:8080',
        'https://creatives.sascdn.com',
        'https://creatives.simpleads.com.br',
        'https://lab.simpleads.com.br'
      ]

      if (!authorizedDomains.includes(event.origin)) return

      if (data.cmd && data.cmd === 'close') {
        return _blink.destroy()
      }

      _blink.addBaseCSS().then(() => _blink.open(data))
    }
  }

  return _blink
}
