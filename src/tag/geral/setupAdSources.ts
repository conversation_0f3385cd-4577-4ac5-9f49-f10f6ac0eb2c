/* -------------------------------------------------------------------------
Realiza setup de fontes de anúncios da rede (smart, prebid e google)
Injetar scripts da smart, prebid e google se ativo
------------------------------------------------------------------------- */

export function setupAdSources() {
  /** habilitando os serviços do google e ouvinte evento de carregamento de bloco de anúncios
   *  habilitando a configuração padrão do google para os site que foram aprovados no admanager */
  if (pub.status_google) {
    _sds.googletag.push(() => {
      googletag.enableServices()
      googletag.pubads().addEventListener('slotRenderEnded', _sds.enableFormats)
    })
  }

  /** habilitando a configuração padrão do prebid se houver bidder
   *  configurações globais do prebid e requisitando as ssps para a busca de anúncios */
  pbSimpleAds.que.push(function () {
    pbSimpleAds.setConfig({
      cache: {
        url: 'https://prebid.adnxs.com/pbc/v1/cache'
      },
      userSync: {
        userIds: [
          {
            name: 'naveggId',
            storage: { name: 'nvggid', type: 'cookie&html5', expires: 8 }
          },
          {
            name: 'criteo'
          }
        ]
      },
      priceGranularity: {
        buckets: [
          {
            precision: 2,
            min: 0,
            max: 3,
            increment: 0.01
          },
          {
            precision: 2,
            min: 3,
            max: 8,
            increment: 0.05
          },
          {
            precision: 2,
            min: 8,
            max: 20,
            increment: 0.5
          },
          {
            precision: 2,
            min: 20,
            max: 99,
            increment: 1
          }
        ]
      },
      debug: _sds.debug.prebid,
      schain: { validation: 'strict', config: _sds.configSupplyChain }
    })
  })
}
