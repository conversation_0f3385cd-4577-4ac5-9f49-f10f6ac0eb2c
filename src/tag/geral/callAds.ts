/* -- --------------------------------------------------------------------------
FUNÇÃO QUE FAZ A SOLICITAÇÃO DE ANÚNCIOS PARA AS SSPS
-------------------------------------------------------------------------- -- */

export function callAds(format: ICallAds) {
  const adSlot = pub.adSlots[format.tagId]
  const adContainer = d.getElementById(adSlot.code)
  const device = _sds.device

  if (adContainer) {
    _sds.log('[CallRTB]:', format.id)

    const enableVideoInstream = adSlot.code.match(/(slider|excroll|inarticle)/gi)

    //SETANDO REFRESH EM TARGET DE SLOT NO PREBID
    const cTarget = `${_sds.target}refresh=${format.refresh};format_id=${format.id}`
    adSlot.bids = adSlot.bids.map(b => ({ ...b, params: { ...b.params, target: cTarget } }))

    const listSlots = [
      //SLOT PARA ANÚNCIOS DE DISPLAY
      {
        code: adSlot.code,
        bids: adSlot.bids,
        mediaTypes: {
          banner: {
            sizes: adSlot.sizes
          }
        }
      },
      //SLOT PARA ANÚNCIOS DE VÍDEO INSTREAM
      {
        code: adSlot.code,
        bids: adSlot.bids.map(b => ({
          ...b,
          params: {
            ...b.params,
            ...(b.bidder.match(/smart/gi) ? { formatId: 51432 } : { video: { language: 'en' } })
          }
        })),
        mediaTypes: {
          video: {
            context: 'instream',
            playerSize: adSlot.code === 'excroll-rtb' && device === 'desktop' ? [640, 480] : [300, 168],
            mimes: ['video/3gpp', 'video/mp4', 'video/webm'],
            protocols: [1, 2, 3, 4, 5, 6, 7, 8],
            startdelay: 0,
            linearity: 1
          }
        }
      }
    ].slice(0, enableVideoInstream ? undefined : -1)

    pbSimpleAds.que.push(function () {
      pbSimpleAds.requestBids({
        timeout: _sds.PREBID_TIMEOUT,
        adUnits: listSlots,
        bidsBackHandler: prebid => bidsCallbackHandler(prebid)
      })
    })
  }

  // eslint-disable-next-line
  function bidsCallbackHandler(prebid: any) {
    const fid = format.id as keyof typeof _sds.bids
    const bid = (prebid[adSlot.code] || { bids: [{ cpm: 0 }] }).bids.reduce(
      (prevBid: { cpm: number }, currentBid: { cpm: number }) => (prevBid.cpm > currentBid.cpm ? prevBid : currentBid)
    )

    _sds.bids[fid]++

    const configInsertion = _sds.getConfigInsertionDirect(bid.ad)
    bid.origin = bid.bidder === 'smartadserver' && !configInsertion ? ['smartrtb'] : [bid.bidder]

    //TESTE COM OBSERVADOR PARA IMPRIMIR CRIATIVOS SÓ QUANDO O USUÁRIO CHEGAR NA ÁREA DE VISUALIZAÇÃO
    if (adSlot.code.match(/inarticle/gi) && !(configInsertion && parseInt(configInsertion.fields?.print_when) === 1)) {
      const observer = new IntersectionObserver(
        entries => {
          const element = entries[0]

          if (element.isIntersecting) {
            enableFormats()
            observer.unobserve(adSlot.instance.dom.container)
          }
        },
        { threshold: 0, rootMargin: '100px' }
      )
      observer.observe(adSlot.instance.dom.container)
    } else {
      enableFormats()
    }

    function enableFormats() {
      if (configInsertion || (pub.status_google === false && bid.ad) || bid.vastXml) {
        _sds.enableFormats({ ...bid, sds_elementId: adSlot.code, sds_formatId: [format.id], configInsertion })
      } else if (pub.status_google && !!adSlot.path && format.id !== 52026) {
        _sds.googletag.push(requestGoogleSlot)
      } else {
        _sds.enableFormats({ ...bid, sds_elementId: adSlot.code, sds_formatId: [format.id], configInsertion })
      }
    }

    function requestGoogleSlot() {
      adSlot.slot =
        adSlot.slot ||
        googletag.defineSlot(adSlot.path, [...adSlot.sizes, 'fluid'], adSlot.code)!.addService(googletag.pubads())
      //limpando targeting de slot
      adSlot.slot.clearTargeting()
      //setando parametros do prebid no slot do google para competição
      pbSimpleAds.setTargetingForGPTAsync(bid.adUnitCode)
      adSlot.slot
        .setTargeting('domain', pub.domain)
        .setTargeting('sds_formatId', format.id.toString())
        .setTargeting('sds_origin', bid.origin[0])
        .setTargeting('sds_size', bid.size)
      googletag.pubads().refresh([adSlot.slot])
    }

    /* a cada intervalo de tempo, solicita novos anúncios por formato */
    /* exclui formato fullscreen do refresh */
    if (format.id !== 52026) {
      setTimeout(() => callAds({ ...format, refresh: true }), _sds.getRefreshDelay(bid.ad || bid.vastXml, format.id))
    }
  }
}
