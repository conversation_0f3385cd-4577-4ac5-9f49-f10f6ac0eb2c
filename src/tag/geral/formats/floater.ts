export default class Floater {
  /* -------------------------------------------------------------------------
  | STYLES AND HTMLS
  ------------------------------------------------------------------------- */
  private styles = {
    globals: `
      .sds-floater {
        position: fixed;
        bottom: 0;
        z-index: 2147483647;
        margin: 5px;
        pointer-events: all;

        * {
          box-sizing: border-box;
        }
      }
      .sds-fl-slot {
        min-width: 100px;
        min-height: 50px;
        position: relative;
        z-index: 2;
        transition: transform 300ms ease;
      }
      .sds-fl-label {
        position: absolute;
        top: -25px;
        left: 0;
        z-index: 2;
        width: fit-content;
        height: 18px;
        padding: 3px 8px;
        border-radius: 4px;
        color: #666;
        font-size: 12px;
        font-family: Arial;
        font-weight: initial;
        line-height: 1;
        background: #fafafa;
      }
      .sds-fl-close-btn {
        position: absolute;
        top: -28px;
        right: 0;
        display: flex;
        width: 100%;
        height: 28px;
        justify-content: end;
        z-index: 1;
        cursor: pointer;

        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: -20px;
          width: 100%;
          height: 20px;
          background-color: transparent;
        }

        &.sds-hidden .sds-fl-close-label {
          pointer-events: none;
          opacity: 0;
          transform: translateY(10px);
        }
      }
      .sds-fl-close-label {
        position: relative;
        z-index: 2;
        transition: all 200ms ease;

        svg {
          width: 80px;
          height: 28px;
        }
      }
      .sds-fl-close-bar {
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 100%;
        height: 5px;
        background: #fafafa;
        box-shadow: rgba(0, 0, 0, 0.6) 0px -1px 5px -1px, rgba(0, 0, 0, 0.4) 0px 1px 2px -1px;
        z-index: 1;
      }
    `,
    slider: `
      #smp_52892 {
        --s: 0.85;
        right: 0;
        margin: 5px;
        transform-origin: bottom right;
        transform: scale(var(--s));
      }

      @media only screen and (max-width: 600px) {
        #smp_52892 { --s: 0.85; }
      }

      #smp_52892 .sds-closed,
      #smp_52025 .sds-closed { transform: translateX(150%); }

      #smp_52892.sds-sl-left,
      #smp_52025.sds-ed-left { right: inherit; left: 0; }

      #smp_52892.sds-sl-left .sds-closed,
      #smp_52025.sds-ed-left .sds-closed { transform: translateX(-150%); }
    `,
    footer: `
      #smp_52027 {
        left: 50%;
        margin: 0;
        transform: translateX(-50%);

        .sds-closed {
          transform: translateY(150%);
        }

        &.sds-ft-header {
          bottom: inherit;
          top: 0;

          .sds-closed {
            transform: translateY(-150%);
          }
        }
      }
    `,
    edge: `
      #smp_52025 {
        right: 0;
        bottom: inherit;
        top: 50%;
        transform: translateY(-50%);
      }
    `
  }

  private closeArrow = `<path d="m51.998 15 6-6"/><path d="m45.998 9 6 6"/>`
  private closeTimes = `<path d="m45.998 15 6-6"/><path d="m45.998 9 6 6"/>`
  private closeIcon = _sds.config.floater.closeIcon === 'arrow' ? this.closeArrow : this.closeTimes

  private closeSvg = `<svg clip-rule="evenodd" fill-rule="evenodd" stroke-linecap="square" viewBox="0 0 80 25" xmlns="http://www.w3.org/2000/svg"><defs><filter id="dropShadowTop" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feComponentTransfer in="SourceAlpha" result="TransferredAlpha"><feFuncR type="discrete" tableValues="0.5"></feFuncR><feFuncG type="discrete" tableValues="0.5"></feFuncG><feFuncB type="discrete" tableValues="0.5"></feFuncB></feComponentTransfer><feGaussianBlur in="TransferredAlpha" stdDeviation="2"></feGaussianBlur><feOffset dx="0" dy="0" result="offsetblur"></feOffset><feMerge><feMergeNode></feMergeNode><feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs><path d="m80 25.001v-20c-.491-2.906-3.053-5.041-6-5h-44c-2.947-.041-5.509 2.094-6 5v14c0 3.292-2.708 6-6 6z" fill="#fafafa" fill-rule="nonzero" style="filter:url(&quot;#dropShadowTop&quot;)"/><path d="m0 24.001h80v5h-80z" fill="#fafafa"/><g fill="none" stroke="#616161" stroke-width="2">${this.closeIcon}</g></svg>`

  private closeButtonHTML = `<div class="sds-fl-close-label">${this.closeSvg}</div><div class="sds-fl-close-bar"></div>`

  /* -------------------------------------------------------------------------
  | DOM MANIPULATION AND EVENTS
  ------------------------------------------------------------------------- */
  private dom

  private setup: IFormatSetup

  constructor(setup: IFormatSetup) {
    this.setup = setup

    /* -------------------------------------------------------------------------
    | SET FLOATER DOM
    ------------------------------------------------------------------------- */
    this.dom = {
      container: _sds.createEl({
        id: setup.tagId,
        className: `sds-floater${setup.formatId === 52025 ? ' sds-ed-left' : ''}`
      }),
      slot: _sds.createEl({ id: `sds-${setup.formatId}-slot`, className: 'sds-fl-slot sds-closed' }),
      label: _sds.createEl({ type: 'span', className: 'sds-fl-label', html: 'publicidade' }),
      close: _sds.createEl({ type: 'div', className: 'sds-fl-close-btn sds-hidden', html: this.closeButtonHTML }),
      creativeContainer: _sds.createEl({ id: setup.containerId })
    }

    _sds.addStyles(this.styles.globals + this.styles.slider + this.styles.footer + this.styles.edge)

    this.dom.close.addEventListener('click', (e: MouseEvent) => {
      this.dom.container.remove()
      e.preventDefault()
    })

    this.dom.slot.append(this.dom.label, this.dom.close, this.dom.creativeContainer)
    this.dom.container.appendChild(this.dom.slot)

    const absoluteContainer = d.body || d.getElementsByTagName('html')[0]

    absoluteContainer.appendChild(this.dom.container)

    !_sds.ispreview && t.callAds({ id: setup.formatId, tagId: setup.tagId, refresh: false })
  }

  //HABILITANDO BOTÃO DE FECHAR
  private showCloseBtn() {
    const delay = _sds.config.floater.closeDelay
    setTimeout(() => this.dom.close.classList.remove('sds-hidden'), delay)
  }

  //DEFININDO TAMANHO DO SLOT DENTRO DO FORMATO
  public setSize(origin: string, width: number, height: number, configInsertion?: InsertionConfig) {
    this.dom.creativeContainer.removeAttribute('style')
    this.dom.slot.removeAttribute('style')
    this.dom.slot.classList.remove('sds-closed')
    this.showCloseBtn()

    origin !== 'google' && this.dom.slot.setAttribute('style', `width: ${width}px; height: ${height}px;`)

    /* ajustar posição do slider caso footer seja carregado */
    if (this.setup.formatId === 52027) {
      const slider = d.getElementById('smp_52892') || d.getElementById('sas_52892')
      slider && slider.style.setProperty('bottom', `${height + 30}px`)
    }
  }
}
