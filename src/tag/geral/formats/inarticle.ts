export default class InArticle {
  /* -------------------------------------------------------------------------
  | STYLES AND HTMLs
  ------------------------------------------------------------------------- */
  private styles = {
    inarticle: `
      #sds-52896-banner {
        position: relative;
        visibility: visible;
        width: 100%;
        clear: both;
        transition: height 300ms ease;
        overflow: hidden;
        margin: 20px auto !important;

        * {
          box-sizing: border-box;
        }

        &.sds-closed {
          visibility: hidden;
        }
      }
      .sds-ina-slot {
        position: relative;
        z-index: 2;
      }
      .sds-ina-creativeWrapper {
        position: relative;
        z-index: 2;
        display: block;
        max-width: fit-content;
        margin-left: auto;
        margin-right: auto;

        &.sds-responsive {
          padding-top: 56.25%;
          max-width: none;

          .sds-ina-slot {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
          }
        }
      }
      .sds-ina-label {
        position: relative;
        border-radius: 4px;
        text-align: center;
        margin-bottom: 10px;
        height: 14px;
        font-size: 12px;
        line-height: 1;

        &:before {
          content: '';
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          width: 100%;
          height: 1px;
          display: block;
          background: #e1dee4;
          z-index: 1;
        }

        span {
          position: relative;
          z-index: 2;
          padding: 0 4px;
          background: #fff;
          color: #666;
          font-size: 10px;
          font-family: Arial;
          line-height: 1;
        }
      }
    `,
    parallax: `
      .sds-ina-parallax {
        position: absolute;
        bottom: -24px;
        clip: rect(auto auto auto auto);
      }
    `,
    minislider: `
      .sds-ina-slider {
        z-index: 2147483647;
        position: relative !important;
        opacity: 1 !important;
        --s: 0.8;

        .sds-ina-creativeWrapper .sds-ina-slot {
          position: fixed;
          left: 20px;
          bottom: 100px;
          top: auto !important;
          transform: scale(var(--s));
          transform-origin: left bottom;
          width: auto;
          height: auto;

          .sds-ina-label {
            display: none !important;
          }

          .sds-ina-parallax {
            bottom: 0 !important;
          }

          .sds-ina-close-btn {
            display: block !important;
          }
        }
      }

      .sds-ina-close-btn {
        display: none;
        border: none;
        position: absolute;
        top: -10px;
        right: -10px;
        font: normal 20px/1 Arial, sans-serif;
        background: #fff;
        color: #0474cf;
        box-shadow: 0px 0px 10px 0px rgba(0,0,0, 0.2);
        border-radius: 100px;
        width: 25px;
        height: 25px;
        cursor: pointer;
        user-select: none;
        transition: all 200ms ease;
        text-align: center;
        padding: 0;
        margin: 0;
        z-index: 999999;
        opacity: 0;
        pointer-events: none;
        transform: scale(calc((1 / var(--s)) * 1));

        &:hover {
          transform: scale(calc(1.05 + calc(1.05 - var(--s))));
        }
        &:active {
          transform: scale(calc(1 + calc(1 - var(--s))));
        }

        &.sds-visible {
          pointer-events: all !important;
          opacity: 1 !important;
        }
      }
    `
  }

  /* -------------------------------------------------------------------------
  | DOM MANIPULATION AND EVENTS
  ------------------------------------------------------------------------- */
  private dom

  private setup: IFormatSetup

  private events = {
    parallax: undefined as (() => void) | undefined,
    miniSlider: undefined as IntersectionObserver | undefined
  }

  constructor(setup: IFormatSetup) {
    this.setup = setup

    const container = this.getContainer()

    if (!container) {
      _sds.warn('[InArticle]: Container not found')
      return
    }

    /* -------------------------------------------------------------------------
    | DOM MANIPULATION AND EVENTS
    ------------------------------------------------------------------------- */
    this.dom = {
      container,
      slot: _sds.createEl({ id: `sds-${setup.formatId}-slot`, className: 'sds-ina-slot' }),
      label: _sds.createEl({ className: 'sds-ina-label', html: '<span>CONTINUA DEPOIS DA PUBLICIDADE</span>' }),
      close: _sds.createEl({ type: 'button', className: 'sds-ina-close-btn', html: '×' }),
      creativeWrapper: _sds.createEl({ className: 'sds-ina-creativeWrapper' }),
      creativeContainer: _sds.createEl({ id: setup.containerId })
    }

    _sds.addStyles(this.styles.inarticle + this.styles.parallax + this.styles.minislider)

    this.dom.slot.append(this.dom.label, this.dom.creativeContainer, this.dom.close)
    this.dom.creativeWrapper.append(this.dom.slot)
    this.dom.container.append(this.dom.creativeWrapper)

    this.dom.close.addEventListener('click', (e: MouseEvent) => {
      this.removeEvents(true)
      e.preventDefault()
    })

    /* -------------------------------------------------------------------------
    | INIT/OBSERVE INARTICLE FUNCTION
    ------------------------------------------------------------------------- */
    const init = () => !_sds.ispreview && t.callAds({ id: setup.formatId, tagId: setup.tagId, refresh: false })

    if (setup.print_when === 1) {
      init()
    } else {
      const observer = new IntersectionObserver(
        entries => {
          const element = entries[0]

          if (element.isIntersecting) {
            init()
            observer.unobserve(container)
            _sds.log('[inArticle]:', 'inarticle container loaded')
          }
        },
        { threshold: 0, rootMargin: '100px' }
      )

      _sds.log('[inArticle]:', 'observe inarticle container')
      observer.observe(container)
    }
  }

  /* -------------------------------------------------------------------------
  | PREPARE AND SET INARTICLE MAIN CONTAINER
  ------------------------------------------------------------------------- */
  /* Verificar e capturar qual container existente a partir da configuração */
  private getContainer() {
    const publisherContainer = _sds.config.inarticle.container && d.querySelector(_sds.config.inarticle.container)

    const containerSelector = '.sa_incontent, .sa_intext'
    const container = d.querySelector(containerSelector)
    const containerParagraph = container && container.querySelector(':scope > p:nth-of-type(2)')
    const containerSpan = container && container.querySelector(':scope > span:nth-of-type(2)')
    const containerDiv = container && container.querySelector(':scope > div:nth-of-type(2)')
    const containerAny = container && container.querySelector(':scope > :nth-of-type(2)')

    const formatContainer = publisherContainer || containerParagraph || containerSpan || containerDiv || containerAny

    const adContainer = d.createElement('div')
    adContainer.id = `sds-${this.setup.formatId}-banner`
    adContainer.className = 'sds-inarticle sds-closed'

    if (formatContainer) {
      return formatContainer.insertAdjacentElement('afterend', adContainer) as HTMLElement
    } else {
      return null
    }
  }

  /* manipular o modo parallax caso ativo */
  private handleParallaxMode(formatFields?: InsertionConfig['format']) {
    if (formatFields?.parallax) {
      _sds.log('[InArticle]: Parallax mode activated')
      this.dom?.creativeContainer.classList.add('sds-ina-parallax')

      this.events.parallax = () => {
        const scroll = w.scrollY || w.pageYOffset
        const boundsTop = this.dom!.creativeContainer.getBoundingClientRect().top + scroll
        const viewport = { top: scroll, bottom: scroll + w.innerHeight }
        const bounds = { top: boundsTop, bottom: boundsTop + this.dom!.creativeContainer.clientHeight }

        const creativeFrame = this.dom?.creativeContainer.getElementsByTagName('iframe')[0]

        if (
          creativeFrame &&
          ((bounds.bottom > viewport.bottom && viewport.top > bounds.top) ||
            (bounds.bottom < viewport.bottom && viewport.top < bounds.top))
        ) {
          creativeFrame.setAttribute('style', 'position: absolute; bottom: 0;')
        } else if (creativeFrame && bounds.top <= viewport.bottom && bounds.top >= viewport.top) {
          creativeFrame.setAttribute('style', 'position: fixed; bottom: 0;')
        } else if (creativeFrame && bounds.bottom >= viewport.top && bounds.bottom <= viewport.bottom) {
          creativeFrame.setAttribute('style', 'position: fixed; top: 0;')
        }
      }

      d.addEventListener('scroll', this.events.parallax)
    }
  }

  /* manipular modo minislider caso ativo */
  private handleMiniSliderMode(formatFields?: InsertionConfig['format']) {
    if (formatFields?.mini_slider) {
      _sds.log('[InArticle]: MiniSlider mode activated')

      const isSliderVisible = () => !!d.querySelector('#smp_52892 iframe')

      this.events.miniSlider = new IntersectionObserver(
        entries => {
          const element = entries[0]

          if (!element.isIntersecting && element.boundingClientRect.y < 0) {
            if (!formatFields?.mini_slider_prevent || !isSliderVisible()) {
              this.dom?.container.classList.add('sds-ina-slider')
              setTimeout(() => this.dom?.close.classList.add('sds-visible'), 2000)
            }
          } else {
            this.dom?.close.classList.remove('sds-visible')
            this.dom?.container.classList.remove('sds-ina-slider')
          }
        },
        { threshold: 0.7 }
      )

      this.events.miniSlider.observe(this.dom!.container)
    }
  }

  // REMOVENDO EVENTOS E OBSERVERS
  private removeEvents(onlySlider = false) {
    this.dom?.container.classList.remove('sds-ina-slider')
    this.events.miniSlider && this.events.miniSlider.unobserve(this.dom!.container)

    !onlySlider && this.dom?.creativeContainer.classList.remove('sds-ina-parallax')
    !onlySlider && this.events.parallax && d.removeEventListener('scroll', this.events.parallax)

    _sds.fmtfn.inarticle.removeEvents = (oSlider = false) => this.removeEvents(oSlider)
  }

  //DEFININDO TAMANHO DO SLOT DENTRO DO FORMATO
  public setSize(origin: string, width: number, height: number, configInsertion?: InsertionConfig) {
    const formatFields = configInsertion?.format

    this.dom?.creativeContainer.removeAttribute('style')
    this.dom?.slot.removeAttribute('style')
    this.dom?.creativeWrapper.classList[height === 0 ? 'add' : 'remove']('sds-responsive')
    this.dom?.container.classList.remove('sds-closed')
    this.dom?.container.setAttribute('style', `padding-bottom: ${origin !== 'google' ? 24 : 0}px;`)

    this.removeEvents()
    this.handleParallaxMode(formatFields)
    this.handleMiniSliderMode(formatFields)

    const currentWidth = width > 1 ? `${width}px` : '100%'
    const currentHeight = height ? `${height}px` : '100%'

    origin !== 'google' &&
      height &&
      this.dom?.slot.setAttribute('style', `width: ${currentWidth}; height: ${currentHeight};`)
  }
}
