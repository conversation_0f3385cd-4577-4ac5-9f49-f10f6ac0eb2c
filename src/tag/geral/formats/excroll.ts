export default class Excroll {
  /* -------------------------------------------------------------------------
  | STYLES AND HTMLs
  ------------------------------------------------------------------------- */
  private styles = {
    excroll: `
      .sa_container { position: relative; }
      #smp_52894_banner {
        transition: transform 600ms ease;
      }
      .sds-exc-sticky {
        position: -webkit-sticky;
        position: sticky;
        top: 0;
        z-index: 0;
      }
      #sds-52894-wrapper {
        overflow: hidden;
        transform: scaleY(1);
        transform-origin: top;
        transition: all 300ms ease;

        &.sds-closed {
          padding-top: 0 !important;
          transform: scaleY(0) !important;
        }

        &.sds-responsive {
          padding-top: 56.25%;

          .sds-exc-slot {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
          }
        }

        .sds-hidden {
          display: none !important;
        }

        .sds-left {
          left: 0;
        }

        .sds-right {
          right: 0;
        }
      }
      #sds-52894-wrapper, .sds-exc-slot {
        position: relative;
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      @media screen and (min-width: 1024px) {
        #sds-52894-wrapper.sds-responsive { padding-top: 49.25%; }
      }
      .sds-exc-close {
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        position: absolute;
        top: 20px;
        right: 20px;
        font: normal 20px/1 Arial, sans-serif;
        background: #fff !important;
        color: #0474cf !important;
        box-shadow: 0px 0px 30px 0px rgba(4,116,207, 0.5);
        border-radius: 100px;
        width: 25px;
        height: 25px;
        cursor: pointer;
        user-select: none;
        transition: all 200ms ease;
        padding: 0;
        margin: 0;
        z-index: 999999;

        &:hover {
          transform: scale3d(1.1, 1.1, 1.1);
        }

        &:active {
          color: #444;
          transform: scale3d(1, 1, 1);
        }
      }
      .sds-exc-selo {
        position: absolute;
        bottom: 0px;
        z-index: 2;
      }
      .sds-exc-goto {
        position: absolute;
        bottom: 0;
        z-index: 3;
        border: none;
        width: calc(588px / 2);
        height: calc(116px / 2);
        background: url('https://static.simpleads.com.br/goto.png') no-repeat center center;
        background-size: contain;
        background-color: transparent !important;
        cursor: pointer;
        margin: 0;
      }
      @media screen and (max-width: 600px) {
        .sds-exc-goto {
          width: calc(588px / 2.5);
          height: calc(116px / 2.5);
        }
      }
    `,
    minislider: `
      .sds-exc-slider {
        z-index: 2147483647;
        position: relative;
        opacity: 1 !important;
        --s: 0.50;

        #sds-52894-wrapper {
          transform: none !important;

          .sds-exc-slot {
            position: fixed;
            top: auto !important;
            left: 20px;
            bottom: 120px;
            transform: scale(var(--s));
            transform-origin: left bottom;
            width: auto;
            height: auto;

            .sds-exc-close {
              width: 25px;
              height: 25px;
              transform: scale(calc((1 / var(--s)) * 1));
              box-shadow: 0px 0px 10px 0px rgba(0,0,0, 0.2);
              transition: opacity 0.3s ease-in-out;
              opacity: 0;
              top: -10px;
              right: -10px;

              &.sds-visible {
                display: flex !important;
                opacity: 1 !important;
              }
            }
          }
        }
      }
    `
  }

  /* -------------------------------------------------------------------------
  | DOM MANIPULATION AND EVENTS
  ------------------------------------------------------------------------- */
  private dom

  private events = {
    sticky: undefined as (() => void) | undefined,
    miniSlider: undefined as IntersectionObserver | undefined
  }

  constructor(setup: IFormatSetup) {
    /* -------------------------------------------------------------------------
    | PREPARE AND SET EXCROLL MAIN CONTAINER
    ------------------------------------------------------------------------- */
    const container = d.getElementById(setup.tagId + '_banner')

    if (!container) {
      _sds.warn('[Excroll]: Container not found')
      return
    }

    /* -------------------------------------------------------------------------
    | DOM MANIPULATION AND EVENTS
    ------------------------------------------------------------------------- */
    this.dom = {
      container,
      slotWrapper: _sds.createEl({ id: `sds-${setup.formatId}-wrapper`, className: 'sds-closed' }),
      slot: _sds.createEl({ id: `sds-${setup.formatId}-slot`, className: 'sds-exc-slot' }),
      selo: _sds.createEl({
        type: 'span',
        title: 'SimpleAds',
        className: 'sds-exc-selo sds-hidden sds-right',
        html: `<a href="https://simpleads.com.br" target="_blank"><img src="//static.simpleads.com.br/Selo_Small_SimpleAds.png" alt="Logo SimpleAds" style="width: 40px;" /></a>`
      }),
      goto: _sds.createEl({ type: 'button', className: 'sds-exc-goto sds-hidden', title: 'Ir para o conteúdo' }),
      close: _sds.createEl({ type: 'div', className: 'sds-exc-close sds-hidden', html: '×' }),
      creativeContainer: _sds.createEl({ id: setup.containerId })
    }

    _sds.addStyles(this.styles.excroll + this.styles.minislider)
    this.dom.container.classList.add('sds-excroll')

    this.dom.slot.append(this.dom.close, this.dom.creativeContainer, this.dom.selo)
    this.dom.slotWrapper.append(this.dom.slot, this.dom.goto)
    this.dom.container.appendChild(this.dom.slotWrapper)

    this.dom.close.addEventListener('click', (event: Event) => this.handleClose(event))
    this.dom.goto.addEventListener('click', (event: Event) => this.handleClose(event))

    /* -------------------------------------------------------------------------
    | INIT/OBSERVE EXCROLL FUNCTIONS
    ------------------------------------------------------------------------- */
    const init = () => !_sds.ispreview && t.callAds({ id: setup.formatId, tagId: setup.tagId, refresh: false })

    if (setup.print_when === 1) {
      init()
    } else {
      const observer = new IntersectionObserver(
        entries => {
          const element = entries[0]

          if (element.isIntersecting) {
            init()
            observer.unobserve(container)
            _sds.log('[Excroll]:', 'container loaded')
          }
        },
        { threshold: 0, rootMargin: '100px' }
      )

      _sds.log('[Excroll]:', 'observe container')
      observer.observe(container)
    }
  }

  private handleClose(e: Event) {
    this.dom?.slotWrapper.classList.add('sds-closed')
    setTimeout(() => this.dom?.container.remove(), 600)
    this.removeEvents()
    e.preventDefault()
  }

  /* -------------------------------------------------------------------------
  | Setar opacidade e fixação do elemento baseado no seu posicionamento e no scroll
  ------------------------------------------------------------------------- */
  private handleStickyMode = () => {
    this.dom?.container.classList.add('sds-exc-sticky')
    const clientRect = this.dom?.container.getBoundingClientRect()
    const containerPosition = (clientRect?.top || 0) + w.scrollY

    this.events.sticky = () => {
      const elementHeight = Math.round(this.dom?.container.offsetHeight || 0)
      const scrollBlood = elementHeight / 2.5
      const scrollTop = w.scrollY - scrollBlood
      const currentOpacity = parseFloat(this.dom?.container.style.opacity || '0')
      const formulaOpacity = (elementHeight - scrollTop + containerPosition) / elementHeight

      const newOpacity = scrollTop > containerPosition && currentOpacity !== 0 ? Math.max(0, formulaOpacity) : 1

      this.dom!.container.style.opacity = newOpacity.toString()

      if (currentOpacity <= 0.3) {
        this.dom?.container.classList.remove('sds-exc-sticky')
      } else {
        this.dom?.container.classList.add('sds-exc-sticky')
      }
    }
    d.addEventListener('scroll', this.events.sticky)
  }

  /* manipular modo minislider caso ativo */
  private handleMiniSliderMode = (formatFields?: InsertionConfig['format']) => {
    if (formatFields?.mini_slider) {
      _sds.log('[eXcroll]: MiniSlider mode activated')
      const isSliderVisible = () => !!d.querySelector('#smp_52892 iframe')

      this.events.miniSlider = new IntersectionObserver(
        entries => {
          const element = entries[0]

          if (!element.isIntersecting && element.boundingClientRect.y < 0) {
            if (!formatFields?.mini_slider_prevent || !isSliderVisible()) {
              this.dom?.container.classList.add('sds-exc-slider')
              setTimeout(() => this.dom?.close.classList.add('sds-visible'), 2000)
            }
          } else {
            this.dom?.close.classList.remove('sds-visible')
            this.dom?.container.classList.remove('sds-exc-slider')
          }
        },
        { threshold: 0.7 }
      )

      this.events.miniSlider.observe(this.dom!.container)
    }
  }

  /* remover eventos e observers */
  private removeEvents() {
    this.dom?.container.classList.remove('sds-exc-slider')
    this.events.miniSlider && this.events.miniSlider.unobserve(this.dom!.container)

    _sds.fmtfn.excroll.removeEvents = () => this.removeEvents()
  }

  private isBigHeight(height: number) {
    return (_sds.device === 'mobile' && height >= 400) || (_sds.device === 'desktop' && height >= 500) || height === 0
  }

  //DEFININDO TAMANHO DO SLOT DENTRO DO FORMATO
  public setSize(origin: string, width: number, height: number, configInsertion?: InsertionConfig) {
    const formatFields = configInsertion?.format

    const bgImage = `no-repeat 100% center /100% 100% url(${formatFields?.background_image})`
    const bgColor = `#${height === 0 ? '000' : formatFields?.background_color || 'eaeaea'}`

    this.dom?.creativeContainer.removeAttribute('style')
    this.dom?.slot.removeAttribute('style')
    this.dom?.slotWrapper.classList.remove('sds-closed')
    this.dom?.slotWrapper.setAttribute('style', `background: ${formatFields?.background_image ? bgImage : bgColor};`)
    this.dom?.slotWrapper.classList[height === 0 ? 'add' : 'remove']('sds-responsive')

    this.removeEvents()
    !this.events.sticky && this.handleStickyMode()
    this.handleMiniSliderMode(formatFields)

    const currentWidth = width > 1 ? `${width}px` : '100%'
    const currentHeight = height ? `${height}px` : '100%'

    origin !== 'google' &&
      height &&
      this.dom?.slot.setAttribute('style', `width: ${currentWidth}; height: ${currentHeight};`)

    const isBHeight = this.isBigHeight(height)

    if (origin === 'smartadserver' && isBHeight) {
      this.dom?.close.classList.remove('sds-hidden')
      this.dom?.selo.classList.remove('sds-hidden')
      this.dom?.goto.classList.add('sds-hidden')
      configInsertion?.format.is_video && this.dom?.selo.classList.replace('sds-right', 'sds-left')
      !configInsertion?.format.is_video && this.dom?.selo.classList.replace('sds-left', 'sds-right')
    } else if (origin !== 'smartadserver' && isBHeight) {
      this.dom?.close.classList.add('sds-hidden')
      this.dom?.selo.classList.add('sds-hidden')
      this.dom?.goto.classList.remove('sds-hidden')
    } else if (!isBHeight) {
      this.dom?.close.classList.add('sds-hidden')
      this.dom?.selo.classList.add('sds-hidden')
      this.dom?.goto.classList.add('sds-hidden')
    }
  }
}
