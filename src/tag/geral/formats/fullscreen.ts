export default class Fullscreen {
  /* -------------------------------------------------------------------------
  | STYLES AND HTMLS
  ------------------------------------------------------------------------- */
  private styles = {
    fullscreen: `
      #fullscreen-rtb {
        border-radius: 4px;
        overflow: hidden;
      }
      .sds-fullscreen {
        position: fixed;
        top: 50%;
        z-index: 2147483647;
        pointer-events: all;
        left: 50%;
        transform: translate(-50%, -50%);

        * {
          box-sizing: border-box;
        }
      }
      .sds-fs-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(1px);
        z-index: 2147483646;
      }
      .sds-fs-slot {
        position: relative;
        z-index: 2;
      }
      .sds-fs-label {
        position: absolute;
        top: -25px;
        left: 0;
        z-index: 2;
        width: fit-content;
        height: 18px;
        padding: 3px 8px;
        border-radius: 4px;
        color:#b5b5b5;
        font-size: 12px;
        font-family: Arial;
        font-weight: initial;
        line-height: 1;
        background: #666;
      }
      .sds-fs-close-label {
        left: inherit;
        right: 0;
        cursor: pointer;
      }
      .sds-fl-close-icon {
        color: #fff
      }
      .sds-fs-close-bar {
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 100%;
        height: 5px;
        background: #fafafa;
        box-shadow: rgba(0, 0, 0, 0.6) 0px -1px 5px -1px, rgba(0, 0, 0, 0.4) 0px 1px 2px -1px;
        z-index: 1;
      }
      .sds-fl-counter {
        margin-right: 5px;
      }
      .sds-fs-closed {
        display: none !important;
        pointer-events: none !important;
      }
    `
  }

  private closeButtonHTML = `<div class="sds-fs-label sds-fs-close-label">fechará automaticamente em: <span class="sds-fl-counter">20s</span> <span class="sds-fl-close-icon">✖</span></div>`

  /* -------------------------------------------------------------------------
  | DOM MANIPULATION AND EVENTS
  ------------------------------------------------------------------------- */
  private dom

  private setup: IFormatSetup

  constructor(setup: IFormatSetup) {
    this.setup = setup

    this.dom = {
      container: _sds.createEl({
        id: setup.tagId,
        className: `sds-fullscreen sds-fs-closed`
      }),
      overlay: _sds.createEl({ id: `sds-${setup.formatId}-overlay`, className: 'sds-fs-overlay sds-fs-closed' }),
      slot: _sds.createEl({ id: `sds-${setup.formatId}-slot`, className: 'sds-fs-slot sds-fs-closed' }),
      label: _sds.createEl({ type: 'span', className: 'sds-fs-label', html: 'publicidade' }),
      close: _sds.createEl({ type: 'div', className: 'sds-fs-close-btn', html: this.closeButtonHTML }),
      creativeContainer: _sds.createEl({ id: setup.containerId })
    }

    _sds.addStyles(this.styles.fullscreen)

    this.dom.close.addEventListener('click', e => this.handleClose(e))

    this.dom.slot.append(this.dom.label, this.dom.close, this.dom.creativeContainer)
    this.dom.container.appendChild(this.dom.slot)

    const absoluteContainer = d.body || d.getElementsByTagName('html')[0]
    absoluteContainer.appendChild(this.dom.container)
    absoluteContainer.appendChild(this.dom.overlay)

    !_sds.ispreview &&
      !_sds.getCookie('_sds_fs_loaded') &&
      t.callAds({ id: setup.formatId, tagId: setup.tagId, refresh: false })
  }

  private handleClose(e: MouseEvent | null = null) {
    if (e) {
      e.preventDefault()
      e.stopPropagation()
    }
    this.dom.container.remove()
    this.dom.overlay.remove()
  }

  private initCounter() {
    const counter = this.dom.close.querySelector('.sds-fl-counter')
    if (!counter) return

    let count = 20
    const interval = setInterval(() => {
      counter.textContent = `${count}s`
      count--

      if (count < 0) {
        clearInterval(interval)
        this.handleClose()
      }
    }, 1000)
  }

  public setSize(origin: string, width: number, height: number, configInsertion?: InsertionConfig) {
    let hasInteraction = false

    const handleUserInteraction = () => {
      if (!hasInteraction) {
        hasInteraction = true

        setTimeout(() => {
          this.dom.creativeContainer.removeAttribute('style')
          this.dom.slot.removeAttribute('style')
          this.dom.slot.classList.remove('sds-fs-closed')
          this.dom.overlay.classList.remove('sds-fs-closed')
          this.dom.container.classList.remove('sds-fs-closed')
          this.dom.slot.setAttribute('style', `width: ${width}px; height: ${height}px;`)

          !_sds.ispreview && _sds.setCookie('_sds_fs_loaded', '1', 1)

          this.initCounter()

          window.removeEventListener('scroll', handleUserInteraction)

          // habilitar click de fechar no overlay após um período
          setTimeout(() => {
            this.dom.overlay.addEventListener('click', e => this.handleClose(e))
          }, 2000)
        }, 2000)
      }
    }

    window.addEventListener('scroll', handleUserInteraction)
  }
}
