/* -- --------------------------------------------------------------------------
CARREGAR DEPENDENCIAS DE SCRIPTS EXTERNOS
-------------------------------------------------------------------------- -- */

export async function loadScriptDependencies() {
  const isChrome = /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor)
  const scripts = [
    { src: '//static.simpleads.com.br/prebid.js?v=2.8' },
    { src: '//securepubads.g.doubleclick.net/tag/js/gpt.js', load: pub.status_google }
  ]

  scripts.forEach(script => {
    if (script.load || typeof script.load === 'undefined') {
      _sds.loadScript(script.src, () => {}, d.head)
    }
  })

  _sds.loadScriptNavegg()

  if (isChrome) {
    await _sds.loadSDSCookies()
  }
  t.loadScriptPolyfills()
}
