import { sds } from './_sds'
import { viewability } from './viewability'
import { loadScriptDependencies } from './loadScriptDependencies'
import { loadScriptPolyfills } from './loadScriptPolyfills'
import { setupAdSources } from './setupAdSources'
import { setupContext } from './setupContext'
import { callPreview } from './callPreview'
import { prepareCallAds } from './prepareCallAds'
import { callAds } from './callAds'
import { blink } from './_blink'
import Floater from './formats/floater'
import InArticle from './formats/inarticle'
import Excroll from './formats/excroll'
import Fullscreen from './formats/fullscreen'

async function start() {
  /* verificar se a tag foi carregada dentro de um iframe */
  if (_sds.inIframe() === true && _cs) {
    return _sds.loadScript(_cs.src, () => {}, wt.document.head, undefined, true)
  }

  if (_sds.currentDomain !== pub.domain) {
    return _sds.warn('Domain not authorized')
  }

  /* configurações de funcionamento */
  _sds.alwaysOnTop()
  w.addEventListener('message', event => _blink.message(event))

  /* executar comandos personalizados */
  _sds.runCmds()
  const preview = _sds.preview()

  if (preview) {
    return t.callPreview(preview)
  }

  /* carregamento de dependências */
  await t.loadScriptDependencies()
  t.setupAdSources()

  /* setar target e contexto */
  await t.setupContext()

  /* seguir o fluxo normal da tag */
  return _sds.config.standalone ? undefined : t.prepareCallAds()
}

export {
  sds,
  viewability,
  start,
  loadScriptDependencies,
  loadScriptPolyfills,
  setupAdSources,
  setupContext,
  callPreview,
  prepareCallAds,
  callAds,
  blink,
  Floater,
  InArticle,
  Excroll,
  Fullscreen
}
