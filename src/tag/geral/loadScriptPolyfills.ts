/* -- --------------------------------------------------------------------------
CARREGAR POLYFILLS EXTERNOS
-------------------------------------------------------------------------- -- */

export function loadScriptPolyfills() {
  return
  // const hasIntersectionObserver =
  //   'IntersectionObserver' in window &&
  //   'IntersectionObserverEntry' in window &&
  //   'intersectionRatio' in window.IntersectionObserverEntry.prototype

  // const polyUri =
  //   '//polyfill.io/v3/polyfill.min.js?features=MutationObserver%2CIntersectionObserver%2CIntersectionObserverEntry'

  // if (!hasIntersectionObserver) {
  //   _sds.loadScript(polyUri, () => {})
  // }
}
