/* -------------------------------------------------------------------------
| Funções globais utilizadas pela tag
------------------------------------------------------------------------- */

export function sds(env: boolean) {
  const url = new URL(w.location.href)
  const configSupplyChain = {
    ver: '1.0',
    complete: 1,
    nodes: [{ asi: 'simpleads.com.br', sid: pub.ad_seller_id.toString(), hp: 1 }]
  }
  const _sds = {
    PREBID_TIMEOUT: 2000,
    googletag: {
      push: (callback: () => void) => {
        if (googletag.apiReady) {
          return callback()
        }

        googletag.cmd.push(() => callback())
      }
    },
    config: {
      standalone: false,
      stdFormats: [] as TFormatIds[],
      vastURL: undefined as string | undefined,
      mgId: {
        siteId: undefined as string | undefined,
        widgetId: undefined as string | undefined
      },
      inarticle: {
        show: true,
        container: undefined as string | undefined
      },
      excroll: {
        show: true,
        containers: ['body > div'],
        styles: {}
      },
      floater: {
        show: true,
        closeDelay: 4500,
        closeIcon: 'arrow'
      },
      fullscreen: {
        show: true
      },
      arroba: {
        containers: undefined as string[] | undefined
      },
      superbanner: {
        containers: undefined as string[] | undefined
      },
      wideskyscrapper: {
        containers: undefined as string[] | undefined
      },
      billboard: {
        containers: undefined as string[] | undefined
      }
    },
    cmd: undefined as (() => void)[] | undefined | { push: (callback: () => void) => void },
    origins: {
      smartadserver: 1,
      smartrtb: 2,
      google: 3,
      rubicon: 7,
      inventory: 99
    },
    formats: {
      fullscreen: 'smp_52026',
      excroll: 'smp_52894',
      footer: 'smp_52027',
      slider: 'smp_52892',
      edge: 'smp_52025',
      inarticle: 'smp_52896'
    },
    bids: { 52026: 0, 52894: 0, 52027: 0, 52892: 0, 52025: 0, 52896: 0 },
    fmtfn: {
      fullscreen: {},
      excroll: {},
      footer: {},
      slider: {},
      edge: {},
      inarticle: {}
    } as Record<string, Record<string, () => void>>,
    //IDS DE CRIATIVOS NA SMART POR ORIGEM DE IMPRESSÃO
    creativeOrigins: <Record<NonNullable<ICountTrackParams['org']>, number>>{
      rubicon: 37423921,
      mgid: 40719142,
      smartrtb: 37423925,
      smartadserver: 37423927,
      google: 37423931
    },
    configSupplyChain,
    user: {
      uid: null as string | null,
      urls: [] as string[],
      cookies: null as string | null,
      contexts: [] as number[]
    },
    navegg: JSON.parse(localStorage.getItem('nvgpersona92531') || '{}') as NaveggInterface,
    target: '',
    currentDomain: w.location.host.replace(/www./g, ''),
    url: url,
    context: { po: [1, 0, 1, 1], ct: [99999], bs: [99999] },
    device: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(w.navigator.userAgent)
      ? 'mobile'
      : 'desktop',
    debug: {
      prebid: !!url.searchParams.get('prebid_debug'),
      sds: env || !!url.searchParams.get('sds_debug'),
      campaign: url.searchParams.get('sds_campaign') || undefined
    },
    setup: (config: ISdsConfig) => Object.assign(_sds.config, config),
    loadFormat: (fid: TFormatIds) => {
      if (!_sds.config.stdFormats.includes(fid) && pub.formats.some(f => f.id === fid)) {
        _sds.config.stdFormats.push(fid)
        t.prepareCallAds(fid)
        return
      }
      return _sds.warn(`Format ${fid} is invalid or already loaded`)
    },
    runCmds: () => {
      const cmd = w._sds.cmd

      const push = (cb: () => void) => {
        if (typeof cb === 'function') {
          return cb()
        }

        _sds.warn(cb, 'is not a function')
      }

      w._sds.cmd = { push }

      if (!cmd || (Array.isArray(cmd) && cmd.length === 0)) return

      try {
        Array.isArray(cmd) && cmd.forEach(push)
      } catch (error) {
        _sds.warn('Failed to execute cmd:', error)
      }
    },
    //PEGANDO CONFIGURAÇÃO DE INSERÇÃO DAS CAMPANHAS DIRETAS SE EXISTIR
    getConfigInsertionDirect: (textConfig: string): InsertionConfig | undefined => {
      if (!_sds.verifyCampaignDirect(textConfig)) {
        return
      }

      let lines = textConfig.replace(/(<([^>]+)>)/gi, '').split('\n')
      lines = lines.slice(0, -6)
      lines = lines.filter(line => !line.trim().startsWith('//'))
      const textSasConfig = lines.join('\n')

      const script = d.createElement('script')
      script.type = 'text/javascript'
      script.text = textSasConfig
      d.head.appendChild(script)

      const formatFields = sas_config

      script.remove()

      return formatFields
    },
    naveggSmartString: () => {
      return Object.keys(_sds.navegg)
        .reduce((acc: string, key: string) => acc + `nvg_${key}=${_sds.navegg[key as keyof typeof _sds.navegg]};`, '')
        .toString()
    },
    ispreview: false,
    preview: () => {
      const id = _sds.url.searchParams.get('sds_preview')

      if (!id) return false

      const format = (_sds.url.searchParams.get('format') || '').toLowerCase() as keyof typeof _sds.formats
      const creativeId = _sds.url.searchParams.get('creative_id') || ''
      const tagId = _sds.formats[format] || ''
      const formatId = Number(tagId.replace(/(smp_|sas_)/gi, '')) as TFormatIds | undefined
      const is = String(id).match(/^[0-9]+$/g) && String(format).match(/[A-Za-z]\w+/i)
      const url = `//previewcore.smartadserverapis.com/preview?ruid=52577&insid=${id}&fid=${formatId}&tag=${tagId}${
        creativeId ? `&cid=${creativeId}` : ''
      }`

      const preview = is ? { id, format, formatId, tagId, url } : false
      _sds.ispreview = Boolean(preview)

      return preview
    },
    track: function ({ org = 'inventory', fid, iid = undefined }: ICountTrackParams) {
      if (_sds.ispreview) {
        return
      }

      const timestamp = new Date().getTime()
      const refresh = _sds.bids[fid] > 1

      /* se não for campanha direta, disparar o pixel de onNoad para contagem de inventário na smart */
      if (org !== 'smartadserver') {
        _sds.createPixel(
          `https://use2.smartadserver.com/h/aip?tmstp=${timestamp}&siteid=${pub.smart_site_id}&pgid=${pub.smart_page_id}&fmtid=${fid}&envtype=0&statid=1&visit=s&tgt=${_sds.target}refresh=${refresh};`
        )
      }

      /* contando impressão única por formato por página */
      const countUniqueImpressions = !refresh && org !== 'inventory'

      if (countUniqueImpressions) {
        const smartCreativeId = _sds.creativeOrigins[org]
        _sds.createPixel(
          `https://www15.smartadserver.com/track/action?sid=${timestamp}&pid=${pub.smart_page_id}&iid=12332264&fmtid=${fid}&cid=${smartCreativeId}&ts=${timestamp}&key=otherAdInteraction&value=otherAdInteraction`
        )
      }

      if (iid && org === 'smartadserver') {
        // disparo para api de tracking pixel do elastic (wip)
      }
    },
    loadScriptNavegg: () => {
      const o = 'Navegg'

      if (!w[o] && !w.nvg92531) {
        const a = d.createElement('script')
        a.src = 'https://tag.navdmp.com/universal.min.js'

        const b = d.getElementsByTagName('script')[0]
        b.parentNode!.insertBefore(a, b)

        w[o] =
          w[o] ||
          function (parms: unknown) {
            w[o].q = w[o].q || []
            // @ts-ignore
            w[o].q.push([this, parms])
          }
      }

      if (w.nvg92531) {
        return _sds.warn(`nvg92531 is already loaded`)
      }

      w.nvg92531 = new Navegg({ acc: 92531 })
    },
    loadSDSCookies: () => {
      return new Promise(resolve => {
        const iframeUrl = 'https://static.simpleads.com.br/who/'
        const iframeExists = d.querySelector(`iframe[src="${iframeUrl}"]`)

        if (iframeExists) {
          _sds.log('[Cookies Iframe]: iframe already exists')
          return resolve(true)
        }

        const messageHandler = (e: MessageEvent) => {
          if (e.data.type && e.data.type === 'sds-cks') {
            _sds.log('[Cookies]:', 'Message', e.data)

            // filtrar cookies somente para os que contenham sds_
            const cookiesArray = e.data.cks.split(';').filter((cookie: string) => cookie.trim().startsWith('sds_'))
            const filteredCookies = cookiesArray.join(';').replace(/\s+/g, '')

            _sds.user.cookies = !_sds.user.cookies && filteredCookies.length > 0 ? filteredCookies : null
            w.removeEventListener('message', messageHandler)
            resolve(true)
          }
        }

        w.addEventListener('message', messageHandler)

        const iframe = d.createElement('iframe')
        iframe.src = iframeUrl
        iframe.style.display = 'none'

        // adiciona um timeout para resolver a promise
        const timeout = setTimeout(() => {
          if (!_sds.user.cookies) {
            _sds.warn('[Cookies]: timeout waiting for message (cookies can still be sent in the req)')
            resolve(false)
          } else {
            resolve(true)
          }

          clearTimeout(timeout)
        }, 300)

        iframe.onload = () => {
          _sds.log('[Cookies]: iframe loaded')
        }

        d.body.appendChild(iframe)
      })
    },
    getGNetworkId: () => pub.ad_network_code,
    log: (...messages: unknown[]) => {
      if (_sds.debug.sds) {
        console.log('%cSDS', 'background: #0074D9; color: #fff; padding: 0 5px; border-radius: 4px;', ...messages)
      }
    },
    warn: (...messages: unknown[]) => {
      console.warn('%cSDS', 'background-color: #FFA500; color: #000; border-radius: 4px;', ...messages)
    },

    /* -------------------------------------------------------------------------
    | Criar elementos HTML
    ------------------------------------------------------------------------- */
    createEl: ({ type = 'div', id = '', className = '', styles = '', html = '', title = '' }) => {
      const el = d.createElement(type)

      if (id.length > 0) el.id = id
      if (className.length > 0) el.className = className
      if (styles.length > 0) el.style.cssText = styles
      if (html.length > 0) el.innerHTML = html
      if (title.length > 0) el.title = title

      return el
    },

    /* -------------------------------------------------------------------------
    | Limpar [sas_creativeClickUrl] da smart quando o retorno é muito grande com tags ngv_ e $wpc
    | Evita impressão em branco da Space (url too large)
    | Evita não contabilização de eventos de clicks dos ADServers
    | DCM Macro Regex: script.match('-dcm-') | /data-dcm-click-tracker='([^']+)'/
    ------------------------------------------------------------------------- */
    formatScriptCode(script: string) {
      const isSpace = script.match('space.ad')

      if (!isSpace) {
        return script
      }

      let regex: RegExp | null = null

      if (isSpace) {
        regex = /setMacro\('(.*)'\)/
      }

      if (!regex) {
        return script
      }

      const url = script.match(regex)

      if (!url || url[1].length < 1500) {
        return script
      }

      const newUrl = _sds.cleanSmartMacro(url[1])
      const newScript = script.replace(url[1], newUrl)

      return newScript
    },

    cleanSmartMacro(macro: string) {
      if (macro.length < 1500) {
        return macro
      }

      _sds.log('[nvg_, $wpc]: cleaned')

      const url = decodeURIComponent(macro)
      return url.split('nvg_')[0] + '&pgDomain=' + url.split('&pgDomain=').pop()
    },

    /* -------------------------------------------------------------------------
    | Criar tracking pixel
    | @param url - Url do pixel de trackeamento
    ------------------------------------------------------------------------- */
    createPixel: function (url: string) {
      if (!url) return
      const px = new Image()
      px.src = url
    },

    /* -------------------------------------------------------------------------
    | Contar Pixels de impressão
    | @param sds - objeto que vem da smart com informações do criativo
    ------------------------------------------------------------------------- */
    countPrintPixels: function (sds: InsertionConfig) {
      _sds.createPixel(sds.creative.countPixelUrl)

      if (sds.fields.custom_pixel) {
        _sds.createPixel(sds.fields.custom_pixel)
      }
    },

    /* -------------------------------------------------------------------------
    | Enviar via Post Message a Click Tag de criativos html5
    | @param sds - objeto que vem da smart com informações do criativo
    | @param dom - elementos utilizados no DOM do formato
    ------------------------------------------------------------------------- */
    sendPostMessageClickTag: function (sds: InsertionConfig, dom: ISvelteDom) {
      // @ts-ignore
      dom.creativeFrame.contentWindow.postMessage(
        JSON.stringify({
          campaignId: sds.campaignId,
          insertionId: sds.insertionId,
          pageId: sds.pageId,
          formatId: sds.formatId,
          actionPixel: sds.fields.action_pixel,
          trackUrl: sds.creative.trackUrl,
          clickUrls: sds.creative.clickUrlArray,
          clickCountPixelUrl: sds.creative.clickCountPixelUrl,
          externalVideo: sds.fields.external_video,
          isSmart: true
        }),
        '*'
      )
    },

    /* -------------------------------------------------------------------------
    | Função que habilitar a visualização dos formatos padrões no RTB
    ------------------------------------------------------------------------- */
    enableFormats: function (e: IFormatBid) {
      const format = Number((e.sds_formatId || (e.slot && e.slot.getTargeting('sds_formatId')) || [1])[0])
      const origin = (e.origin || (e.slot && e.slot.getTargeting('sds_origin')) || [''])[0]

      if (!format) {
        return
      }

      const isGoogleCreativeThird = e.size && e.size[0] === 1 && e.size[1] === 1
      const isGoogleWithoutCreative = !e.size
      const tagId = `smp_${format}`

      const ad: IRenderBidData = {
        adID: e.adId,
        ad: e.ad || '',
        vastUrl: e.vastUrl || '',
        elID: e.sds_elementId || e.slot.getSlotElementId(),
        tagId,
        slot: pub.adSlots[tagId],
        fid: format as keyof typeof _sds.bids,
        //@ts-ignore NÃO MEXER ESTÁ FUNCIONANDO
        origin: !e.isEmpty && e.campaignId && !isGoogleCreativeThird ? 'google' : origin,
        size:
          isGoogleCreativeThird || isGoogleWithoutCreative
            ? ((e.slot && e.slot.getTargeting('sds_size')) || [''])[0]
            : e.size,
        container: () => d.getElementById(ad.elID) as HTMLElement,
        isGoogleCreative: !!(!e.isEmpty && e.campaignId)
      }

      _sds.log('[EnableFormat]:', ad)

      // GAMBIARRA PARA IMPRIMIR ANÚNCIOS QUE O GOOGLE BIZARRAMENTE NÃO IMPRIME
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const currentHighestBid: any = (pbSimpleAds.getHighestCpmBids && pbSimpleAds.getHighestCpmBids(ad.elID)[0]) || {}
      ad.adID = ad.adID || currentHighestBid.adId
      ad.ad = ad.ad || currentHighestBid.ad

      if (ad.isGoogleCreative || ad.adID) {
        const pbContainer = !ad.isGoogleCreative ? ad.container() : null
        ad.size = (typeof ad.size === 'string' ? ad.size.split(/x/i) : ad.size || [0, 0]).map(size => Number(size))

        ad.slot.instance.setSize(ad.origin, ad.size[0], ad.size[1], e.configInsertion)

        if (pbContainer) {
          _sds.renderPrebid(pbContainer, ad)
        }
      } else if (ad.elID === 'slider-rtb') {
        if (_sds.config.mgId.siteId && _sds.config.mgId.widgetId) {
          _sds.renderMgid(ad)
          return
        } else if (_sds.config.vastURL) {
          _sds.fetchRequestVideo(_sds.config.vastURL, ad)
        }
      }

      _sds.track({ org: ad.origin, fid: ad.fid })
    },

    createIframeAds: function (container: HTMLElement) {
      const childs = Array.from(container.childNodes)
      childs.map(child => child.remove())

      const iframe = d.createElement('iframe')
      iframe.scrolling = 'no'
      iframe.frameBorder = 'none'
      container.prepend(iframe)

      const css = d.createElement('style')
      css.type = 'text/css'
      css.appendChild(d.createTextNode('body { margin: 0 !important; padding: 0 !important; }'))

      return { iframe, css }
    },
    /* -------------------------------------------------------------------------
    | Carregando script da Mgid
    ------------------------------------------------------------------------- */
    renderMgid: function (ad: IRenderBidData) {
      const { iframe, css } = _sds.createIframeAds(ad.container())

      const containerMgid = d.createElement('div')
      containerMgid.setAttribute('data-type', '_mgwidget')
      containerMgid.setAttribute('data-widget-id', _sds.config.mgId.widgetId || '')

      const script = d.createElement('script')
      script.type = 'text/javascript'
      script.async = true
      script.src = `https://jsc.mgid.com/site/${_sds.config.mgId.siteId}.js`

      iframe.width = '100%'
      iframe.height = '100%'
      iframe.onload = () => setTimeout(() => _sds.showAdMgid(ad, iframe), 3000)
      iframe.contentWindow?.document.open()
      iframe.contentWindow?.document.write(script.outerHTML + containerMgid.outerHTML + css.outerHTML)
      iframe.contentWindow?.document.close()
    },
    /* -------------------------------------------------------------------------
    | OBSERVANDO ELEMENTOS PARA MOSTRAR ANÚNCIOS DA MGID
    ------------------------------------------------------------------------- */
    showAdMgid: function (ad: IRenderBidData, frame: HTMLIFrameElement) {
      const docFrame = frame.contentWindow?.document
      const firstDivContainerMgid = docFrame?.querySelector('div')
      const shadowDocument = firstDivContainerMgid?.shadowRoot
      const firstDivShadow = shadowDocument?.querySelector('div')
      const containerAd = firstDivShadow?.querySelector('.mgbox')
      const origin = 'mgid'

      const firstDivShadowStyle = firstDivShadow && getComputedStyle(firstDivShadow)
      const containerAdStyle = containerAd && getComputedStyle(containerAd)

      const visible = firstDivShadowStyle?.visibility
      const width = containerAdStyle?.width.replace(/px/gi, '')
      const height = containerAdStyle?.height.replace(/px/gi, '')

      if (visible === 'visible' && width && height) {
        ad.slot.instance.setSize(origin, Number(width), Number(height), {})
        _sds.track({ org: origin, fid: ad.fid })
      } else {
        _sds.track({ fid: ad.fid })
      }
    },
    /* -------------------------------------------------------------------------
    | Tratando e retornando delay para atualização de anúncios
    ------------------------------------------------------------------------- */
    getRefreshDelay: function (bidAd?: string, fid?: number) {
      const formatDelay = fid === 52892 ? 10 : 20
      const delayCampaignDirect = ((bidAd || '').match(/(refresh_delay:.*)/gi) || [''])[0]
      const delayVideoRTB = (bidAd || '').match(/(<vast.*>)/gi) ? 30 : ''
      const delayNumber = Number(delayCampaignDirect.replace(/\D/g, '') || delayVideoRTB || formatDelay) * 1000
      _sds.log('[Refresh Delay]:', fid, `${delayNumber}ms`)

      return delayNumber
    },

    /* -------------------------------------------------------------------------
    | Verificando se é uma campanha direta
    ------------------------------------------------------------------------- */
    verifyCampaignDirect: function (bidAd?: string) {
      return (bidAd || '').match(/(https:\/\/)static\.simpleads\.com\.br\/banner/gi)
    },

    /* -------------------------------------------------------------------------
    | Função que vai renderizar os criativos de Prebid de sites que são bloqueados no Admanager
    ------------------------------------------------------------------------- */
    renderPrebid: function (container: HTMLElement, bid: IRenderBidData) {
      const { iframe, css } = _sds.createIframeAds(container)

      iframe.width = Array.isArray(bid.size) && Number(bid.size[0]) > 1 ? String(bid.size[0]) : '100%'
      iframe.height = Array.isArray(bid.size) && Number(bid.size[1]) ? String(bid.size[1]) : '100%'

      const absoluteHTML = bid.vastUrl || bid.vastXml ? _sds.generateHtmlPlayer(bid) : bid.ad

      iframe.contentWindow?.document.open()
      iframe.contentWindow?.document.write(absoluteHTML + css.outerHTML)
      iframe.contentWindow?.document.close()
    },

    //REQUISITANDO VAST URL E RECUPERANDO XML
    fetchRequestVideo: function (vastURL: string, ad: IRenderBidData) {
      fetch(vastURL)
        .then(async res => await res.text())
        .then(vastXML => (ad = { ...ad, vastXml: vastXML.match(/(<ad.*>)/gi) ? vastXML : '' }))
        .then(ad => (ad = { ...ad, size: ad.vastXml ? [300, 168] : ad.size }))
        .then(ad => {
          //@ts-ignore
          ad.vastXml && ad.slot.instance.setSize(ad.origin, ad.size[0], ad.size[1], {})
          ad.vastXml && _sds.renderPrebid(ad.container(), ad)
        })
    },

    //CARREGANDO VAST XML STRING
    generateHtmlPlayer: function (bid: IRenderBidData) {
      const sas_config = {
        formatId: bid.fid,
        pageId: pub.smart_page_id,
        tagId: bid.tagId,
        creative: {
          type: 2,
          url: '',
          scriptCode: bid.vastUrl || bid.vastXml,
          width: Array.isArray(bid.size) ? bid.size[0] : 0,
          height: Array.isArray(bid.size) ? bid.size[1] : 0
        },
        fields: {
          print_when: '1 : Chegar na área de visualização'
        },
        format: {
          is_video: true
        }
      }

      const videoString = `
        const script = document.createElement('script');
        script.onload = () => new SimpleAdsBanner(${JSON.stringify(sas_config)});
        script.async = false;
        script.src = 'https://static.simpleads.com.br/banner/bundle.video.js';
        document.head.appendChild(script);
      `

      const script = d.createElement('script')
      script.type = 'text/javascript'
      script.innerHTML = videoString

      return script.outerHTML
    },

    /* -------------------------------------------------------------------------
    | Função que verifica se a tag está sendo carregada dentro de um iframe
    ------------------------------------------------------------------------- */
    inIframe: () => ws !== wt,

    /* -------------------------------------------------------------------------
    | Inserir script na página e executar um callback caso necessário
    ------------------------------------------------------------------------- */
    loadScript(src: string, callback?: () => void, target: HTMLElement = d.head, id?: string, force: boolean = false) {
      const load = force || !d.querySelector(`script[src="${src}"]`)

      if (load) {
        const script = d.createElement('script')
        script.src = src
        script.async = true
        script.dataset['cfasync'] = 'false'

        if (_cs.hasAttribute('data-standalone')) {
          script.setAttribute('data-standalone', 'true')
        }

        if (id) script.id == id
        if (callback) script.onload = callback

        return target.appendChild(script)
      }
    },

    /* -------------------------------------------------------------------------
    | Função para observar existência de um elemento e executar um callback
    ------------------------------------------------------------------------- */
    observerElement: (selector: string, callback: () => void) => {
      const observer = new MutationObserver(function (mutationsList, observer) {
        for (const mutation of mutationsList) {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            for (const node of Array.from(mutation.addedNodes)) {
              if (node instanceof Element && node.matches(selector)) {
                _sds.log('[ObserverElement]:', selector, callback)
                callback()
                observer.disconnect()
              }
            }
          }
        }
      })

      const config = { childList: true, subtree: true }
      observer.observe(d, config)
    },

    /* -------------------------------------------------------------------------
    | Função que adiciona uma folha de estilos no header do publisher
    ------------------------------------------------------------------------- */
    addStyles: function (syles: string) {
      if (syles) {
        const css = d.createElement('style')
        css.appendChild(d.createTextNode(syles))
        d.head.appendChild(css)
      }
    },

    /* -------------------------------------------------------------------------
    | Função que adiciona uma classe a um elemento se existir
    ------------------------------------------------------------------------- */
    addClass: (container: Array<string> | string, className: string) => {
      const containerId = Array.isArray(container) ? container.filter(c => !!d.querySelector(c))[0] : container
      const el = d.querySelector(containerId)

      if (el) {
        _sds.log('[AddClass]:', containerId, className)
        return el.classList.add(className)
      }

      _sds.observerElement(containerId, () => _sds.addClass(containerId, className))
    },

    /* -------------------------------------------------------------------------
    | Função que prepara o eXcroll (em processo de depreciação para o config.excroll)
    ------------------------------------------------------------------------- */
    preExcroll(container: HTMLElement | string, styles: { [key: string]: string } = {}) {
      const containerId = Array.isArray(container) ? container.filter(c => !!d.querySelector(c))[0] : container
      const target = d.querySelector(containerId)
      const excroll = d.createElement('div')
      excroll.id = 'smp_52894_banner'

      if (target && !d.querySelector(`#${excroll.id}`)) {
        Object.keys(styles).forEach(s => (target.style[s] = styles[s]))
        target.classList.add('sa_container')
        target.insertAdjacentElement('beforebegin', excroll)
      }
    },

    /* -------------------------------------------------------------------------
    | Deixar os formatos acima de todas as outras redes
    ------------------------------------------------------------------------- */
    alwaysOnTop() {
      const css = d.createElement('style')
      css.appendChild(
        d.createTextNode(
          'body ins.adsbygoogle.adsbygoogle-noablate, body ._cm-os-slider, body vli, ins[id^="gpt_unit_"] { z-index: 2147483646 !important; } body.sds-noscroll { height: 100vh; overflow: hidden; } div[id*="-rtb"] { width: 100%; height: 100%; background: #eaeaea; iframe { position: relative; z-index: 2; margin: 0; padding: 0; border: 0; } }'
        )
      )
      d.head.appendChild(css)
    },

    /* -------------------------------------------------------------------------
    | Manipular Cookies
    ------------------------------------------------------------------------- */
    getCookie(cookieName: string) {
      const name = cookieName + '='
      const cookies = d.cookie.split(';')

      for (let cookie of cookies) {
        cookie = cookie.trim()

        if (cookie.startsWith(name)) {
          return cookie.substring(name.length)
        }
      }

      return null
    },

    setCookie(name: string, value: string, expirationHours: number) {
      const date = new Date()
      date.setTime(date.getTime() + expirationHours * 60 * 60 * 1000)
      const expires = 'expires=' + date.toUTCString()
      d.cookie = name + '=' + value + ';' + expires + ';path=/;domain=.' + pub.domain
    },

    removeCookie(name: string) {
      d.cookie = name + '=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
    },

    /* -------------------------------------------------------------------------
    | Gerar ID aleatório para salvar cookie de usuário
    ------------------------------------------------------------------------- */
    randID(size = 16) {
      const ts = Date.now()
      let id = ''

      while (id.length < size) {
        id += Math.floor(Math.random() * ts)
          .toString(36)
          .slice(2)
      }

      id = id.slice(0, size)

      const lID = id.slice(0, Math.ceil(id.length / 2))
      const rID = id.slice(Math.ceil(id.length / 2))

      return `${lID}${ts.toString(36)}${rID}`
    },
    saveUserID() {
      let uid = _sds.getCookie('_sds_uid')

      if (!uid) {
        uid = _sds.randID()
        _sds.log('[Save UserID]:', uid)
        _sds.setCookie('_sds_uid', uid, 8760)
      }

      return uid
    },
    setupUserData() {
      if (_sds.user && _sds.user.uid) {
        return _sds.user
      }

      const uid = _sds.saveUserID()
      const user = {
        ..._sds.user,
        uid
      }

      _sds.log('[UserData]:', user)
      return user
    }
  }

  window._sds = Object.assign({}, window._sds, {
    // @ts-ignore
    nvg: _sds.navegg,
    device: _sds.device,
    refresh: _sds.bids,
    fmtfn: _sds.fmtfn,
    url: _sds.url,
    setup: _sds.setup,
    loadFormat: _sds.loadFormat,
    getGNetworkId: _sds.getGNetworkId,
    formatScriptCode: _sds.formatScriptCode,
    cleanSmartMacro: _sds.cleanSmartMacro,
    createPixel: _sds.createPixel,
    countPrintPixels: _sds.countPrintPixels,
    track: _sds.track,
    sendPostMessageClickTag: _sds.sendPostMessageClickTag,
    addStyles: _sds.addStyles,
    addClass: _sds.addClass,
    preExcroll: _sds.preExcroll,
    getCookie: _sds.getCookie,
    setCookie: _sds.setCookie,
    removeCookie: _sds.removeCookie,
    log: _sds.log,
    warn: _sds.warn
  })

  return _sds
}
