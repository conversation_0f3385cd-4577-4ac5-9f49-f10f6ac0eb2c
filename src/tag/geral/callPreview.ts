/* -- --------------------------------------------------------------------------
Carregamento de preview na url da página se preciso passar dois paramêtros:
sds_preview = id da inserção na smart
format = nome do formato
ex: https://publisher.tld/?sds_preview=11791689&format=slider&&creative_id=xxxxx
@TODO: Remover a dependencia de formatos externos (necessário formatos dentro da tag)
-------------------------------------------------------------------------- -- */

export async function callPreview(preview: Exclude<ReturnType<typeof _sds.preview>, false>) {
  async function getPreviewConfig() {
    const request = await fetch(preview.url, { method: 'GET', cache: 'no-store' })
    const textConfig = await request.text()

    const sas_config = _sds.getConfigInsertionDirect(textConfig)

    return { sas_config, textConfig }
  }

  const { sas_config, textConfig } = await getPreviewConfig()

  const adSlot = pub.adSlots[preview.tagId]

  if (!adSlot) {
    _sds.warn(`Adslot ${preview.tagId} not found`)
    return
  }

  const bid: Partial<IFormatBid> = {
    ad: `<html><body><script type="text/javascript">${textConfig}</script></body></html>`,
    sds_elementId: adSlot.code,
    sds_formatId: [Number(preview.formatId)],
    configInsertion: sas_config,
    adId: preview.id,
    origin: ['smartadserver'],
    size: [Number(sas_config?.creative?.width) || 0, Number(sas_config?.creative?.height) || 0]
  }

  t.prepareCallAds(preview.formatId)

  _sds.enableFormats(bid as IFormatBid)
}
