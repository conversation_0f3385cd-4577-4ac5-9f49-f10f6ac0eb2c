/* -------------------------------------------------------------------------
Configura e carrega os formatos de anúncios
------------------------------------------------------------------------- */

export async function prepareCallAds() {
  pub.formats.map(format => {
    const adSlot = pub.adSlots[format.tagId],
      isMobile = _sds.device === 'mobile',
      sizeCutNote = 450

    const filterSizes = adSlot.sizes.filter(ls => (isMobile ? ls[0] < sizeCutNote : ls[0] > sizeCutNote))

    adSlot.sizes = filterSizes.length ? filterSizes : adSlot.sizes

    if (!d.getElementById(adSlot.code)) {
      //VERIFICANDO SE EXISTE UM CONTAINER PAI PARA CARREGAMENTO DO ANÚNCIO
      const slotCode = adSlot.code as SlotCodeIABS
      const containerIds = _sds.config[slotCode] && _sds.config[slotCode].containers
      const containerId = (containerIds || []).filter(c => !!d.querySelector(c))[0]
      const containerEl = d.querySelector(containerId)

      const renderDiv = d.createElement('div')
      renderDiv.id = adSlot.code

      if (containerEl) {
        containerEl.insertAdjacentElement('afterend', renderDiv)
      } else {
        d.body.appendChild(renderDiv)
      }
    }

    t.callAds({ ...format, refresh: false })
  })
}
