import { sds } from './_sds'
import { prepareCallAds } from './prepareCallAds'
import { callAds } from './callAds'

async function start() {
  const pathName = window.location.pathname

  if (
    _sds.currentDomain === 'sandbox.estadao.com.br' &&
    pathName === '/emais/projetos-reais-impulsionam-o-aprendizado/'
  ) {
    console.log('Página teste estadão')
  } else if (_sds.currentDomain !== pub.domain) {
    return _sds.warn('Domain not authorized')
  } else if (pub.domain === 'estadao.com.br') {
    return _sds.warn('Domain in the certification phase')
  }

  /* executar comandos personalizados */
  const preview = _sds.preview()

  if (preview) {
    return t.callPreview(preview)
  }

  /* carregamento de dependências */
  await t.loadScriptDependencies()
  t.setupAdSources()

  /* setar target e contexto */
  await t.setupContext()

  /* seguir o fluxo normal da tag */
  return t.prepareCallAds()
}

export { sds, start, prepareCallAds, callAds }
