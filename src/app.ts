import { join } from 'path'
import { FastifyPluginAsync } from 'fastify'
import { serializerCompiler, validatorCompiler } from 'fastify-type-provider-zod'
import AutoLoad, { AutoloadPluginOptions } from '@fastify/autoload'
import { headersMiddleware } from './middlewares/headersMiddleware'
import { errorHandlerMiddleware } from './middlewares/errorHandlerMiddleware'

export type AppOptions = Partial<AutoloadPluginOptions>

const app: FastifyPluginAsync<AppOptions> = async (fastify, options): Promise<void> => {
  /* -------------------------------------------------------------------------
  | Middlewares
  ------------------------------------------------------------------------- */
  fastify.addHook('preHandler', headersMiddleware)

  /* -------------------------------------------------------------------------
  | Error Handler Response
  ------------------------------------------------------------------------- */
  fastify.setErrorHandler((e, r, p) => errorHandlerMiddleware(e, r, p))

  // Add schema validator and serializer
  fastify.setValidatorCompiler(validatorCompiler)
  fastify.setSerializerCompiler(serializerCompiler)

  /* -------------------------------------------------------------------------
  | Carregar todos os plugins definidos na pasta ./plugins
  | Plugins de suporte que serão utilizados em toda a aplicação
  ------------------------------------------------------------------------- */
  fastify.register(AutoLoad, { dir: join(__dirname, 'plugins'), options })

  /* -------------------------------------------------------------------------
  | Carregar todas as rotas definidas na pasta ./routes
  ------------------------------------------------------------------------- */
  fastify.register(AutoLoad, { dir: join(__dirname, 'routes'), options })

  /* rota para verificar se o serviço está online */
  fastify.get('/', (req, reply) => reply.send({ status: 'online' }))
}

const options = { disableRequestLogging: true, trustProxy: true }

export default app
export { app, options }
