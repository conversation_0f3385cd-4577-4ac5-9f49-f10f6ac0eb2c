export type SiteIntegration = {
  id: string
  domain: string
  url: string
  smart_site_id: number
  smart_page_id: number
  custom_code: string
  active_admanager: boolean
  active_rtb: boolean
  admanager_network_id: string
  seller_id: number
  formats: Array<{
    site_id: string
    active: boolean
    name: string
    smart_format_id: number
    ad_sizes: Array<string>
    created_at: Date
    updated_at: Date
  }>
  easyConfig?: {
    blocklist: Array<string>
    seeAlsoQty: number
    seeAlsoPos: string
    openEngagedWord: boolean
    sendGAId: boolean
    markingLevel: number
    allow_google: boolean
    seeAlso: boolean
    ad_network_code: number
    glTapToSearch: boolean
    customColor: string
    googleAnalytics: null
    domain: string
    limit: number
    adBlockerLeavel: number
    context: string
    mouseoverTime: number
    smart_site_id: number
    smart_page_id: number
    ad_seller_id: number
  }
}

export interface Sizes {
  desktop: Array<number[]>
  mobile: Array<number[]>
}

export interface MediaTypes {
  banner: Banner
}

export interface Banner {
  sizes: Sizes
}
