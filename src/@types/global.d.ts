/// <reference types="googletag" />
/// <reference types="google.analytics" />

/* -------------------------------------------------------------------------
| Tipagem para variáveis de ambiente
------------------------------------------------------------------------- */
namespace NodeJS {
  interface ProcessEnv {
    FASTIFY_PORT: string
    ENVIRONMENT: 'dev' | 'prod'
    PRO_IP_API_URL: string
    PARENT_AD_NETWORK_ID: string
    LRU_CACHE: string
    LRU_CACHE_TTL: string
    CF_API_BASE_URL: string
    CF_EMAIL: string
    CF_KEY: string
    CF_ZONE: string
    DASH_API_BASE_URL: string
    DASH_API_ACCESS_TOKEN: string
  }
}

/* -------------------------------------------------------------------------
| Evento de ativação de formato
------------------------------------------------------------------------- */
type TFormatIds = 52026 | 52894 | 52027 | 52892 | 52025 | 52896
type TIabFormatIds = 52026 | 51545 | 52141 | 52144 | 52143

interface IFormatBid extends googletag.events.SlotRenderEndedEvent {
  ad?: string
  vastXml?: string
  vastUrl?: string
  sds_elementId?: string
  sds_formatId?: [string] | [number]
  configInsertion?: Partial<InsertionConfig>
  origin?: ['smartadserver' | 'smartrtb' | 'google' | 'inventory']
  adId?: string
  bidder?: ['smartadserver' | 'smartrtb' | 'google' | 'inventory']
  cpm?: number
  mediaType?: string
}

interface IRenderBidData {
  ad: string
  vastXml: string
  vastUrl: string
  adID: string | undefined
  slot: AdSlot
  elID: string
  tagId: string
  fid: TFormatIds
  origin: 'smartadserver' | 'smartrtb' | 'google' | 'inventory'
  size: string | number[] | null
  container: () => HTMLElement
  isGoogleCreative: boolean
  isGoogleNative: () => boolean | null
}

interface IFormatSetup {
  // general
  formatId: TFormatIds
  tagId: string
  containerId: AdSlot['code']
  print_when: number
}

interface ICallAds {
  id: number
  tagId: string
  refresh: boolean
}

/* -------------------------------------------------------------------------
| Variáveis globais da rede
------------------------------------------------------------------------- */
interface ISdsConfig {
  standalone?: boolean
  stdFormats?: TFormatIds[]
  inarticle?: {
    container?: string
  }
  arroba?: {
    containers?: string[]
  }
  superbanner?: {
    containers?: string[]
  }
  wideskyscrapper?: {
    containers?: string[]
  }
  billboard?: {
    containers?: string[]
  }
  excroll?: {
    containers?: string[]
    styles: { [key: string]: string }
  }
}

interface InsertionConfig {
  campaignId: number
  insertionId: number
  formatId: number
  pageId: number
  tagId: string
  containerId?: string
  sasBaseurl: string
  creative: {
    id: number
    type: number
    url: string
    scriptCode: string
    customCode: string
    clickUrl: string
    clickUrlArray: string[]
    clickCountPixelUrl: string
    countPixelUrl: string
    viewPixelUrl: string
    trackUrl: string
    width: string | number
    height: string | number
  }
  fields: {
    print_when: string
    print_zone: number
    custom_pixel: string
    action_pixel: string
    refresh_delay: string
    params: string
    external_video: string
  }
  format: {
    is_video: boolean
    mini_slider: boolean
    mini_slider_prevent: boolean
    parallax: boolean
    background_color: string
    background_image: string
  }
}

interface ISvelteDom {
  parallax: HTMLElement
  slot: HTMLElement
  videoContainer: HTMLElement
  banner: HTMLElement
  creativeFrame: HTMLIFrameElement | HTMLElement
}

declare interface NaveggInterface {
  gender: string
  age: string
  education: string
  marital: string
  income: string
  connection: string
  city: string
  region: string
  country: string
  everyone: string
  custom: string[]
  brand: string[]
  interest: string[]
  product: string[]
  career: string[]
  everybuyer: string[]
  pgender: string
  page: string
  peducation: string
  pmarital: string
  pincome: string
  lookalike: string[]
}

interface IPublisher {
  adSlots: { [key: string]: AdSlot }
  domain: string
  formats: Array<{
    id: TFormatIds
    tagId: string
  }>
  id: number
  ad_network_code: number
  ad_seller_id: number
  smart_page_id: number
  smart_site_id: number
  status_google: boolean
  active_rtb: boolean
  size: string
}

type SlotCodeIABS = 'arroba' | 'superbanner' | 'wideskyscrapper' | 'billboard'

interface AdSlot {
  code: 'fullscreen-rtb' | 'excroll-rtb' | 'footer-rtb' | 'slider-rtb' | 'inarticle-rtb' | 'edge-rtb'
  slot?: googletag.Slot
  instance: class
  path: string
  bids: Bid[]
  sizes: Array<[number, number]>
}

interface Bid {
  bidder: 'smartadserver' | 'rubicon'
  params: Array<{
    accountId?: number
    siteId?: number
    zoneId?: number
    networkId?: number
    siteId?: number
    pageId?: number
    formatId?: number
  }>
}

interface ICountTrackParams {
  org?: 'smartadserver' | 'smartrtb' | 'google' | 'rubicon' | 'mgid' | 'inventory'
  fid: TFormatIds
  iid?: number
}

/* -------------------------------------------------------------------------
| Display Plus
------------------------------------------------------------------------- */
interface IDataBlink {
  type: 'dplus' | 'blink'
  target: 'easycontent'
  sds: Record<string, string | string[] | number | boolean>
  clickTag: string[]
  url: string
  cmd?: string
  defaultClose?: boolean
  html?: string
  callback?: string
}
