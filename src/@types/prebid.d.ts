/* eslint-disable @typescript-eslint/ban-types */

///

interface SchainNode {
  asi: string
  sid: string
  hp: number
}

interface SchainConfig {
  ver: string
  complete: number
  nodes: SchainNode[]
}

interface Schain {
  validation: 'strict'
  config: SchainConfig
}

interface PriceBucket {
  precision: number
  min?: number
  max: number
  increment: number
}

interface PriceGranularity {
  buckets: PriceBucket[]
}

type PbjsConfig = {
  bidderTimeout?: number
  priceGranularity?: PriceGranularity
  userSync?: unknown
  debug?: boolean
  consentManagement?: ConsentManagement
  realTimeData?: unknown
  schain?: Schain
  cache?: {
    url?: string
  }
}

interface IPBSimpleAds {
  addAdUnits: []
  adUnits: () => void
  aliasBidder: () => void
  aliasRegistry: object
  bidderSettings: () => void
  createBid: () => void
  enableAnalytics: () => void
  getAdserverTargeting: () => void
  getAdserverTargetingForAdUnitCode: () => void
  getAdserverTargetingForAdUnitCodeStr: () => void
  getAllPrebidWinningBids: () => void
  getAllWinningBids: () => void
  getBidResponses: () => void
  getBidResponsesForAdUnitCode: () => void
  getConfig: () => void
  getConsentMetadata: () => void
  getEncryptedEidsForSource: () => void
  getEvents: () => void
  getHighestCpmBids: (adUnitCode?: string) => Array<object>
  getHighestUnusedBidResponseForAdUnitCode: () => void
  getNoBids: () => void
  getNoBidsForAdUnitCode: () => void
  getUserIds: () => void
  getUserIdsAsEidBySource: () => void
  getUserIdsAsEids: () => void
  getUserIdsAsync: () => void
  installedModules: string[]
  libLoaded: boolean
  markWinningBidAsUsed: () => void
  mergeBidderConfig: () => void
  mergeConfig: () => void
  offEvent: () => void
  onEvent: () => void
  processQueue: () => void
  que: {
    push: (cb: () => void) => void
  }
  readConfig: () => void
  refreshUserIds: () => void
  registerAnalyticsAdapter: () => void
  registerBidAdapter: () => void
  registerSignalSources: () => void
  removeAdUnit: () => void
  renderAd: (doc: Document | undefined, id: string | undefined) => void
  requestBids(requestObj?: {
    adUnitCodes?: string[]
    adUnits?: PrebidAdUnit[]
    timeout?: number
    bidsBackHandler?: (bidResponses: unknown, timedOut: boolean, auctionId: string) => void
    labels?: string[]
    auctionId?: string
  }): void
  setBidderConfig: () => void
  setConfig: (config: PbjsConfig) => void
  setTargetingForAst: () => void
  setTargetingForGPTAsync: (adUnits: Array<string>) => void
  triggerBilling: () => void
  triggerUserSyncs: () => void
  version: string
}
