import { sds } from '../tag/geral/_sds'
import { loadScriptDependencies } from '../tag/geral/loadScriptDependencies'
import { loadScriptPolyfills } from '../tag/geral/loadScriptPolyfills'
import { setupAdSources } from '../tag/geral/setupAdSources'
import { callPreview } from '../tag/geral/callPreview'
import { prepareCallAds } from '../tag/geral/prepareCallAds'
import { setupContext } from '../tag/geral/setupContext'
import { callAds } from '../tag/geral/callAds'
import googletag from 'googletag'
import { blink } from '../tag/geral/_blink'
import { Excroll, Floater, Fullscreen, InArticle } from '../tag/geral/tag'

interface ITagFunctions {
  loadScriptDependencies: typeof loadScriptDependencies
  loadScriptPolyfills: typeof loadScriptPolyfills
  setupAdSources: typeof setupAdSources
  setupContext: typeof setupContext
  callPreview: typeof callPreview
  prepareCallAds: typeof prepareCallAds
  callAds: typeof callAds
  sds: typeof sds
  blink: typeof blink
  Floater: typeof Floater
  InArticle: typeof InArticle
  Excroll: typeof Excroll
  Fullscreen: typeof Fullscreen
}

interface NaveggOptions {
  acc: number
}

declare global {
  declare const _cs: HTMLScriptElement
  declare const _sds: ReturnType<typeof sds>
  declare const _blink: ReturnType<typeof blink>
  const tag: ITagFunctions
  const t: ITagFunctions
  const pub: IPublisher
  const pbSimpleAds: IPBSimpleAds
  const w: Window
  const wt: Window
  const ws: Window
  const d: Document
  class Navegg {
    constructor(options: NaveggOptions)
  }
  const nvg44561: Navegg

  const sas_config: InsertionConfig

  declare interface Window {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    Navegg: any
    nvg44561: unknown
    nvg92531: unknown
    preExcroll: () => void
    _sds: Partial<ReturnType<typeof sds>>
    googletag: googletag
  }
}
