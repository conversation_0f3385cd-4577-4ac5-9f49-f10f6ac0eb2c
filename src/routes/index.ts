import { FastifyPluginAsync } from 'fastify'
import fp from 'fastify-plugin'
import { CacheControllerInstance } from '../controllers/cache/CacheController'
import { TagControllerInstance } from '../controllers/tag/TagController'
import { TagInterface, TagRouterOptions } from '../controllers/tag/tagSchema'

const routes: FastifyPluginAsync = async (fastify, options) => {
  fastify.get<TagInterface>('/show.js', TagRouterOptions, TagControllerInstance)
}

const cache: FastifyPluginAsync = async (fastify, options) => {
  fastify.get('/cache/:task', CacheControllerInstance)
}

export default fp(async (fastify, options) => {
  fastify.register(routes, { prefix: '/v3/s:smart_site_id/p:smart_page_id' })
  fastify.register(routes, { prefix: '/v2/s:smart_site_id/p:smart_page_id' })
  fastify.register(routes, { prefix: '/s:smart_site_id/p:smart_page_id' })
  fastify.register(cache)
})
