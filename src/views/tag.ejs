var _sds = window._sds || {};
_sds.cmd = _sds.cmd || [];

var googletag = googletag || {};
googletag.cmd = googletag.cmd || [];

var pbSimpleAds = pbSimpleAds || {};
pbSimpleAds.que = pbSimpleAds.que || [];

(function (w, d) {
  const _cs = d.currentScript
  const csEndsWithJS = _cs.src.endsWith('.js')

  // manter compatibilidade com a tag legada
  if (!csEndsWithJS) {
    const allowedFormats = [52894, 52027, 52892, 52025, 52896]
    const formatId = Number(_cs.src.match(/\d+$/) || 0)

    if (allowedFormats.includes(formatId)) {
      w._sds.cmd.push(function() {
        w._sds.setup({ standalone: true })
        w._sds.loadFormat(formatId)
      })
    }
  }

  if (typeof window._sds.setup !== 'undefined') {
    console.warn(`\x1b[43m\x1b[30m SDS \x1b[0m`, 'Already started or duplicated tag')
    _cs.remove()
    return
  }

  if (_cs.dataset['standalone'] && csEndsWithJS) {
    w._sds.cmd.push(function() {
      w._sds.setup({ standalone: true })
    })
  }

  _cs.id = 'adSimpleAds'
  const ws = window.self
  const wt = window.top
  const pub = <%- site %>
  const t = tag = {
    <% Object.keys(tag).forEach((fnName) => { %>
      <%- fnName %>: <%- tag[fnName] %>,
    <% }); %>
  }

  const _sds = t._sds(<%- env %>)
  const _blink = t._blink()
  w._sds.va = () => t.viewability()

  <% if (custom_code) { %>
    const device = _sds.device // legado com alguns custom_codes
    try { <%- custom_code %> } catch (error) { console.error(error); }
  <% } %>

  // excroll fallback
  if (typeof w.preExcroll === 'undefined') {
    w.preExcroll = function() {
      _sds.warn('preExcroll() is not defined on publisher panel');
      _sds.preExcroll('body > div', {});
    };
  }

  tag.start()

})(window, document)
