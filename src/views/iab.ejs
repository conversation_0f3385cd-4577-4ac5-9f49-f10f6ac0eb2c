var _sds = window._sds || {};
_sds.cmd = _sds.cmd || [];

var googletag = googletag || {};
googletag.cmd = googletag.cmd || [];

var pbSimpleAds = pbSimpleAds || {};
pbSimpleAds.que = pbSimpleAds.que || [];

(function (w, d) {
  const _cs = d.currentScript

  if (typeof window._sds.setup !== 'undefined') {
    console.warn(`\x1b[43m\x1b[30m SDS \x1b[0m`, 'Already started or duplicated tag')
    _cs.remove()
    return
  }

  const ws = window.self
  const wt = window.top
  const pub = <%- site %>
  const t = tag = {
    <% Object.keys(tag).forEach((fnName) => { %>
      <%- fnName %>: <%- tag[fnName] %>,
    <% }); %>
  }

  const _sds = t._sds(<%- env %>)
  w._sds.va = () => t.viewability()

  <% if (custom_code) { %>
    const device = _sds.device // legado com alguns custom_codes
    try { <%- custom_code %> } catch (error) { console.error(error); }
  <% } %>

  setTimeout(() => tag.start(), 1800)

})(window, document)
