const parentNetworkID = process.env.PARENT_AD_NETWORK_ID

const smartBidParams = { networkId: 2447, siteId: '@siteId', pageId: '@pageId', formatId: '@formatId' }

const magniteBidParams = { accountId: 26622, siteId: 547248 }

/*
  - PROPRIEDADES SLOT E PATH - GOOGLE ADMANAGER
  - PROPRIEDADE BIDS - PREBID
*/

/* <!-- SLOTS PARA FORMATOS --> */
const adSlotsFormats = JSON.stringify({
  smp_52026: {
    code: 'fullscreen-rtb',
    bids: [
      {
        bidder: 'smartadserver',
        params: smartBidParams
      }
    ]
  },
  smp_52894: {
    code: 'excroll-rtb',
    bids: [
      {
        bidder: 'smartadserver',
        params: smartBidParams
      },
      {
        bidder: 'rubicon',
        params: { ...magniteBidParams, zoneId: 3412144 }
      }
    ],
    slot: null,
    path: `/${parentNetworkID}@childNetworkID/simpleads/excroll`
  },
  smp_52027: {
    code: 'footer-rtb',
    bids: [
      {
        bidder: 'smartadserver',
        params: smartBidParams
      },
      {
        bidder: 'rubicon',
        params: { ...magniteBidParams, zoneId: 3412148 }
      }
    ],
    slot: null,
    path: `/${parentNetworkID}@childNetworkID/simpleads/footer`
  },
  smp_52892: {
    code: 'slider-rtb',
    bids: [
      {
        bidder: 'smartadserver',
        params: smartBidParams
      },
      {
        bidder: 'rubicon',
        params: { ...magniteBidParams, zoneId: 3409728 }
      }
    ],
    slot: null,
    path: `/${parentNetworkID}@childNetworkID/simpleads/slider`
  },
  smp_52896: {
    code: 'inarticle-rtb',
    bids: [
      {
        bidder: 'smartadserver',
        params: smartBidParams
      },
      {
        bidder: 'rubicon',
        params: { ...magniteBidParams, zoneId: 3412136 }
      }
    ],
    slot: null,
    path: `/${parentNetworkID}@childNetworkID/simpleads/inarticle`
  },
  smp_52025: {
    code: 'edge-rtb',
    bids: [
      {
        bidder: 'smartadserver',
        params: smartBidParams
      },
      {
        bidder: 'rubicon',
        params: { ...magniteBidParams, zoneId: 3412150 }
      }
    ],
    slot: null,
    path: `/${parentNetworkID}@childNetworkID/simpleads/edge`
  }
})

/* <!-- SLOTS PARA CRIATIVOS IAB --> */
const adSlotsIabs = JSON.stringify({
  smp_51545: {
    code: 'arroba',
    bids: [
      {
        bidder: 'smartadserver',
        params: smartBidParams
      },
      {
        bidder: 'rubicon',
        params: { ...magniteBidParams, zoneId: 3633898 }
      }
    ],
    slot: null,
    path: `/${parentNetworkID}@childNetworkID/simpleads/arroba`
  },
  smp_52141: {
    code: 'superbanner',
    bids: [
      {
        bidder: 'smartadserver',
        params: smartBidParams
      },
      {
        bidder: 'rubicon',
        params: { ...magniteBidParams, zoneId: 3633900 }
      }
    ],
    slot: null,
    path: `/${parentNetworkID}@childNetworkID/simpleads/superbanner`
  },
  smp_52144: {
    code: 'billboard',
    bids: [
      {
        bidder: 'smartadserver',
        params: smartBidParams
      },
      {
        bidder: 'rubicon',
        params: { ...magniteBidParams, zoneId: 3633902 }
      }
    ],
    slot: null,
    path: `/${parentNetworkID}@childNetworkID/simpleads/billboard`
  },
  smp_52143: {
    code: 'wideskyscraper',
    bids: [
      {
        bidder: 'smartadserver',
        params: smartBidParams
      },
      {
        bidder: 'rubicon',
        params: { ...magniteBidParams, zoneId: 3633904 }
      }
    ],
    slot: null,
    path: `/${parentNetworkID}@childNetworkID/simpleads/wideskyscrapper`
  }
})

export { adSlotsFormats, adSlotsIabs }
