/* -------------------------------------------------------------------------
| Fastify LRU Cache plugin
------------------------------------------------------------------------- */

import { FastifyError, FastifyInstance, FastifyPluginOptions } from 'fastify'
import fp from 'fastify-plugin'
import { LRU, lru } from 'tiny-lru'

function lruPlugin(fastify: FastifyInstance, options: FastifyPluginOptions, done: (err?: FastifyError) => void) {
  const cache = lru(500, Number(process.env.LRU_CACHE_TTL), true) // padrão 5 minutos
  fastify.decorate('cache', cache)

  done()
}

declare module 'fastify' {
  export interface FastifyInstance {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    cache: LRU<any>
  }
}

export default fp(lruPlugin)
