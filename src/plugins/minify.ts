/* -------------------------------------------------------------------------
| Fastify Terser Minify plugin
------------------------------------------------------------------------- */

import { FastifyError, FastifyInstance, FastifyPluginOptions } from 'fastify'
import fp from 'fastify-plugin'
import { MinifyOptions, MinifyOutput, minify } from 'terser'

function terserPlugin(fastify: FastifyInstance, options: FastifyPluginOptions, done: (err?: FastifyError) => void) {
  const terser = (input: string, terserOptions?: MinifyOptions) => minify(input, terserOptions)
  fastify.decorate('terser', terser)

  done()
}

declare module 'fastify' {
  export interface FastifyInstance {
    terser(input: string, terserOptions?: MinifyOptions): Promise<MinifyOutput>
  }
}

export default fp(terserPlugin)
