import z from 'zod'
import { RouteShorthandOptions } from 'fastify'

/* -------------------------------------------------------------------------
| REQUEST
------------------------------------------------------------------------- */

const tagParamsSchema = z.object({
  smart_site_id: z
    .string()
    .regex(/\d{1,10}/g, 'O id do site é inválido')
    .transform(sid => Number(sid)),
  smart_page_id: z
    .string()
    .regex(/\d{1,10}/g, 'O id da página é inválido')
    .transform(pid => Number(pid))
})

const tagQuerySchema = z.object({
  size: z.optional(z.enum(['arroba', 'superbanner', 'billboard', 'wideskyscrapper']))
})

interface TagInterface {
  Params: (typeof tagParamsSchema)['_output']
  Querystring: (typeof tagQuerySchema)['_output']
}

/* -------------------------------------------------------------------------
| RESPONSE
------------------------------------------------------------------------- */

/* -------------------------------------------------------------------------
| VALIDATION OPTIONS
------------------------------------------------------------------------- */
const TagRouterOptions: RouteShorthandOptions = {
  schema: {
    params: tagParamsSchema,
    querystring: tagQuerySchema
  }
}

export { TagInterface, TagRouterOptions }
