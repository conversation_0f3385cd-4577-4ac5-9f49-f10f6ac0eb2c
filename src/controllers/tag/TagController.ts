/* -------------------------------------------------------------------------
| Gerenciador principal dos dados recebidos do banco de dados
------------------------------------------------------------------------- */
import { FastifyReply, FastifyRequest, FastifyInstance } from 'fastify'
import { adSlotsFormats, adSlotsIabs } from '../../utils/adSlots'
import { TagInterface } from './tagSchema'
import { SiteIntegration } from '../../@types/site'
import dashClient from '../../clients/dash'
import * as TagFormat from '../../tag/geral/tag'
import * as TagIAB from '../../tag/iab/tag'

class TagController {
  private request: FastifyRequest<TagInterface>
  private reply: FastifyReply
  private fastify: FastifyInstance
  private site: SiteIntegration | null
  private sid: number
  private pid: number
  private size: string
  private cacheKey: string

  constructor(request: FastifyRequest<TagInterface>, reply: FastifyReply) {
    this.request = request
    this.reply = reply
    this.fastify = request.server
    this.site = null
    this.sid = this.request.params.smart_site_id
    this.pid = this.request.params.smart_page_id
    this.size = this.request.query.size || ''
    this.cacheKey = `${this.sid}-${this.pid}-${this.size}`
  }

  /**
   * Executa o processo
   */
  public async handler() {
    const cache = this.fastify.cache.get(this.cacheKey)

    if (cache && process.env.LRU_CACHE === 'true') {
      this.fastify.log.info(`Send cache for ${this.cacheKey}`)
      return this.reply.send(cache)
    }

    this.site = await this.getSite()

    if (!this.site.formats.length) {
      throw new Error('Não existem formatos ativos para esse site')
    }

    this.site.formats = this.site.formats
      .filter(f => !f.name.match(/easycontent/gi))
      .filter(f => (!this.size ? !f.name.match(/iab/gi) : this.size === f.name.replace(/(\W|iab)/gi, '').toLowerCase()))

    const currentSite = {
      adSlots: this.getAdSlots(),
      domain: this.site.domain,
      formats: this.site.formats.map(({ smart_format_id: fid }) => ({ id: fid, tagId: this.formatTagId(fid) })),
      id: this.site.id,
      ad_network_code: this.site.admanager_network_id,
      ad_seller_id: this.site.seller_id,
      smart_site_id: this.site.smart_site_id,
      smart_page_id: this.site.smart_page_id,
      status_google: this.site.active_rtb && this.site.active_admanager,
      active_rtb: this.site.active_rtb
    }

    //CONFIGURAÇÕES PADRÃO PARA QUALQUER TIPO DE TAG
    const defaultConfig = {
      _sds: !this.size ? TagFormat.sds : TagIAB.sds,
      start: !this.size ? TagFormat.start : TagIAB.start,
      prepareCallAds: !this.size ? TagFormat.prepareCallAds : TagIAB.prepareCallAds,
      callAds: !this.size ? TagFormat.callAds : TagIAB.callAds,
      viewability: TagFormat.viewability,
      loadScriptDependencies: TagFormat.loadScriptDependencies,
      loadScriptPolyfills: TagFormat.loadScriptPolyfills,
      setupAdSources: TagFormat.setupAdSources,
      setupContext: TagFormat.setupContext,
      callPreview: TagFormat.callPreview
    }

    //CONFIGURAÇÕES PARA OS FORMATOS DIFERENCIADOS
    const formatConfig = {
      _blink: TagFormat.blink,
      Floater: TagFormat.Floater,
      InArticle: TagFormat.InArticle,
      Excroll: TagFormat.Excroll,
      Fullscreen: TagFormat.Fullscreen
    }

    //GERANDO CÓDIGO MINIMIZADO
    const tagScript = await this.fastify.view(`${!this.size ? 'tag' : 'iab'}.ejs`, {
      env: process.env.ENVIRONMENT === 'dev',
      site: JSON.stringify(currentSite),
      custom_code: this.site.custom_code,
      tag: { ...defaultConfig, ...(!this.size ? formatConfig : {}) }
    })

    const tagMinified = await this.fastify.terser(tagScript)
    const tagScriptCleaned = tagMinified.code?.replace(/\s+/g, ' ').replace(/\\n {1,8}/g, '')

    this.fastify.log.info(`Cache for ${this.cacheKey} (${this.site.domain}) created`)
    this.fastify.cache.set(this.cacheKey, tagScriptCleaned)
    return this.reply.send(tagScriptCleaned)
  }

  /**
   * Recupera um site com o smartsiteid e smartpageid fornecidos.
   * @TODO: Verificar se é necessário ou melhorar a gambiarra do ig
   * @param smart_site_id O ID do site na Smart
   * @param smart_page_id O ID da pagina na Smart
   * @returns SiteConfig interface
   */
  private async getSite(): Promise<SiteIntegration> {
    try {
      const response = await dashClient.get('/sites/tag', { params: { siteId: this.sid, pageId: this.pid } })

      return response.data
    } catch (error) {
      throw new Error('Site não encontrado')
    }
  }

  private formatTagId(fid: number) {
    return `smp_${fid}` as keyof typeof adSlotsFormats
  }

  private getAdSlots() {
    const adNetworkCode = this.site?.admanager_network_id

    //SUBSTITUINDO MACROS PADRÕES NOS SLOTS
    const replacedAdSlots = (!this.size ? adSlotsFormats : adSlotsIabs)
      .replace(/(@childNetworkID)+/g, adNetworkCode ? `,${adNetworkCode}` : '')
      .replace(/("@siteId")+/g, String(this.site?.smart_site_id))
      .replace(/("@pageId")+/g, String(this.site?.smart_page_id))

    const currentAdSlots = JSON.parse(replacedAdSlots)

    const filteredFormats = this.site?.formats.filter(f => currentAdSlots[this.formatTagId(f.smart_format_id)])

    return filteredFormats?.reduce((acc, f) => {
      const tagId = this.formatTagId(f.smart_format_id)

      //SUBSTITUINDO MACRO DINÂNIMA POR FORMATO
      const replacedSlot = JSON.stringify(currentAdSlots[tagId]).replace(/("@formatId")+/g, String(f.smart_format_id))

      const formatSlot = JSON.parse(replacedSlot)

      //REMOVENDO PROPRIEDADES SLOT E PATH
      const { slot, path, ...restSlot } = formatSlot

      const currentSlot = this.site?.active_admanager ? formatSlot : restSlot

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      currentSlot.bids = currentSlot.bids.filter((b: any) => this.site?.active_rtb || b.bidder === 'smartadserver')

      const currentSizes = f.ad_sizes.map(s => s.split('x').map(ks => Number(ks)))

      return { ...acc, [tagId]: { ...currentSlot, sizes: currentSizes } }
    }, {})
  }
}

export const TagControllerInstance = (request: FastifyRequest<TagInterface>, reply: FastifyReply) =>
  new TagController(request, reply).handler()
