import z from 'zod'
import { RouteShorthandOptions } from 'fastify'

/* -------------------------------------------------------------------------
| REQUEST
------------------------------------------------------------------------- */

const cacheRequestSchema = z.object({
  task: z.enum(['clear', 'info', 'del'])
})

const cacheRequestQuerySchema = z.object({
  key: z.optional(z.string())
})

interface CacheInterface {
  Params: (typeof cacheRequestSchema)['_output']
  Querystring: (typeof cacheRequestQuerySchema)['_output']
}

/* -------------------------------------------------------------------------
| RESPONSE
------------------------------------------------------------------------- */

/* -------------------------------------------------------------------------
| VALIDATION OPTIONS
------------------------------------------------------------------------- */
const CacheRouterOptions: RouteShorthandOptions = {
  schema: {
    params: cacheRequestSchema,
    querystring: cacheRequestQuerySchema
  }
}

export { CacheInterface, CacheRouterOptions }
