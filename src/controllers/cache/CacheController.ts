/* -------------------------------------------------------------------------
| Controla funcionamento da rota de cache
------------------------------------------------------------------------- */
import { FastifyReply, FastifyRequest, FastifyInstance } from 'fastify'
import { CacheInterface } from './cacheSchema'
import cloudflareClient from '../../clients/cloudflare'

class CacheController {
  private request: FastifyRequest<CacheInterface>
  private reply: FastifyReply
  private fastify: FastifyInstance
  private task: CacheInterface['Params']['task']
  private key: string | undefined

  constructor(request: FastifyRequest<CacheInterface>, reply: FastifyReply) {
    this.request = request
    this.reply = reply
    this.fastify = request.server
    this.task = request.params.task
    this.key = request.query.key
  }

  /**
   * Executa o processo
   */
  public handler = () => this[this.task]()

  private info = () => this.fastify.cache.keys().length

  private clear = async () => {
    this.fastify.cache.clear()
    const cloudflare = await this.clearCloudflare()

    return this.reply.send({ satus: 'clear', cf: cloudflare.success, t: Date.now() })
  }

  private del = () => {
    if (this.key) {
      this.fastify.cache.delete(this.key)
      return this.reply.send({ satus: 'clear', t: Date.now() })
    }

    return this.reply.send({ error: 'missing key' })
  }

  private async clearCloudflare(): Promise<{ success: boolean }> {
    const response = await cloudflareClient.post(`/client/v4/zones/${process.env.CF_ZONE}/purge_cache`, {
      purge_everything: true
    })

    return response.data
  }
}

export const CacheControllerInstance = (request: FastifyRequest<CacheInterface>, reply: FastifyReply) =>
  new CacheController(request, reply).handler()
