/* -- --------------------------------------------------------------------------
Middleware para controlar alguns headers da Tag
Setar/Verificar Caches, CORS, Headers etc
-------------------------------------------------------------------------- -- */

import { FastifyReply, FastifyRequest, HookHandlerDoneFunction } from 'fastify'

export function headersMiddleware(request: FastifyRequest, reply: FastifyReply, done: HookHandlerDoneFunction) {
  const path = request.url

  if (path.includes('show.js')) {
    reply.header('Cache-Control', 'public, max-age=3600')
    reply.header('Content-Type', 'application/javascript')
  }

  if (path.includes('cache')) {
    reply.header('Cache-Control', 'max-age=0, no-store, no-cache, must-revalidate')
    reply.header('Pragma', 'no-cache')
    reply.header('Expires', '0')
  }

  done()
}
